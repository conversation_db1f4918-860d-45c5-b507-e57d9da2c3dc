#include <Windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <tchar.h>
#include <thread>
#include <atomic>
#include <string>
#include <vector>
#include <iostream>
#include <MinHook.h>
#include "imgui/imgui.h"
#include "imgui/imgui_impl_win32.h"
#include "imgui/imgui_impl_dx11.h"
#include "memory.h"
#include "entity.h"
#include "math.h"

// Forward declare message handler from imgui_impl_win32.cpp
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);

// Globals
static ID3D11Device*           g_pd3dDevice = nullptr;
static ID3D11DeviceContext*    g_pd3dDeviceContext = nullptr;
static IDXGISwapChain*         g_pSwapChain = nullptr;
static ID3D11RenderTargetView* g_mainRenderTargetView = nullptr;

static HWND                   g_hGameWindow = nullptr;
static WNDPROC                g_OriginalWndProc = nullptr;

static std::atomic<bool>      g_Running{ true };
static std::thread            g_Thread;

static Memory mem;
static uintptr_t moduleBase = 0;
static uintptr_t localPlayerAddr = 0;
static int playerCount = 0;
static glmatrixf viewMatrix;

struct Config {
    bool showBoxes = true;
    bool showHealth = true;
    bool showName = true;
    bool showWeapons = true;
    bool showAmmo = true;
    bool showDistance = true;
    bool showTeam = true;
    bool showArmor = true;
    bool showState = true;  // Scoping, shooting, etc.

    float boxThickness = 1.0f;
    ImVec4 enemyColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
    ImVec4 teamColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
    ImVec4 textColor = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
} config;

// Forward declarations
void CreateRenderTarget(IDXGISwapChain* pSwapChain);
void CleanupRenderTarget();
LRESULT CALLBACK WndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
HRESULT InitializeImGui();
void CleanupImGui();
void RenderESP();
void MainThread();
void HookPresent();
void UnhookPresent();

// Create/Cleanup render target
void CreateRenderTarget(IDXGISwapChain* pSwapChain)
{
    ID3D11Texture2D* pBackBuffer;
    pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, NULL, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void CleanupRenderTarget()
{
    if (g_mainRenderTargetView)
    {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = nullptr;
    }
}

// MinHook hook pointer
typedef HRESULT(__stdcall* Present)(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags);
Present oPresent = nullptr;

// Hooked Present function
HRESULT __stdcall hkPresent(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags)
{
    static bool initialized = false;
    if (!initialized)
    {
        if (SUCCEEDED(pSwapChain->GetDevice(__uuidof(ID3D11Device), (void**)&g_pd3dDevice)))
        {
            g_pd3dDevice->GetImmediateContext(&g_pd3dDeviceContext);

            DXGI_SWAP_CHAIN_DESC sd;
            pSwapChain->GetDesc(&sd);
            g_hGameWindow = sd.OutputWindow;

            CreateRenderTarget(pSwapChain);

            InitializeImGui();

            // Attach to game process
            if (!mem.Attach(L"ac_client.exe", L"ac_client.exe"))
            {
                MessageBox(NULL, _T("Failed to attach to ac_client.exe"), _T("Error"), MB_OK | MB_ICONERROR);
                g_Running = false;
            }
            else
            {
                moduleBase = mem.GetModuleBase();
            }

            // Hook WndProc to handle input if needed
            g_OriginalWndProc = (WNDPROC)SetWindowLongPtr(g_hGameWindow, GWLP_WNDPROC, (LONG_PTR)WndProc);

            initialized = true;
        }
        else
        {
            return oPresent(pSwapChain, SyncInterval, Flags);
        }
    }

    // Start ImGui frame
    ImGui_ImplDX11_NewFrame();
    ImGui_ImplWin32_NewFrame();
    ImGui::NewFrame();

    // Update game data
    playerCount = mem.Read<int>(moduleBase + Offsets::PlayerCount);
    localPlayerAddr = mem.Read<uintptr_t>(moduleBase + Offsets::LocalPlayer);
    for (int i = 0; i < 16; i++)
    {
        viewMatrix[i] = mem.Read<float>(moduleBase + Offsets::ViewMatrix + i * sizeof(float));
    }

    // Render ESP
    RenderESP();

    // Rendering
    ImGui::Render();
    const float clear_color[4] = { 0.f, 0.f, 0.f, 0.f };
    g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, NULL);
    g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color);
    ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

    return oPresent(pSwapChain, SyncInterval, Flags);
}

HRESULT InitializeImGui()
{
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    ImGui::StyleColorsDark();

    ImGui_ImplWin32_Init(g_hGameWindow);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

    return S_OK;
}

void CleanupImGui()
{
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();
}

void RenderESP()
{
    // ESP Configuration Menu - Position in top-right corner
    ImGui::SetNextWindowPos(ImVec2(1280 - 300, 0), ImGuiCond_Once);
    ImGui::SetNextWindowSize(ImVec2(300, 400), ImGuiCond_Once);
    if (ImGui::Begin("ESP Settings", nullptr, ImGuiWindowFlags_NoCollapse))
    {
        ImGui::Checkbox("Show Boxes", &config.showBoxes);
        ImGui::Checkbox("Show Health", &config.showHealth);
        ImGui::Checkbox("Show Name", &config.showName);
        ImGui::Checkbox("Show Weapons", &config.showWeapons);
        ImGui::Checkbox("Show Ammo", &config.showAmmo);
        ImGui::Checkbox("Show Distance", &config.showDistance);
        ImGui::Checkbox("Show Team", &config.showTeam);
        ImGui::Checkbox("Show Armor", &config.showArmor);
        ImGui::Checkbox("Show State", &config.showState);
        ImGui::SliderFloat("Box Thickness", &config.boxThickness, 0.5f, 5.0f);
        ImGui::ColorEdit4("Enemy Color", (float*)&config.enemyColor);
        ImGui::ColorEdit4("Team Color", (float*)&config.teamColor);
        ImGui::ColorEdit4("Text Color", (float*)&config.textColor);
        ImGui::End();
    }

    // Loop through entities and draw ESP
    for (int i = 0; i < playerCount; i++)
    {
        uintptr_t entityAddr = mem.Read<uintptr_t>(moduleBase + Offsets::EntityList + i * 0x4);
        if (!entityAddr) continue;

        Entity entity(entityAddr, mem);
        if (!entity.IsAlive()) continue;

        Vec3 pos = entity.GetHeadPosition();
        vec screenPosVec;
        if (Math::WorldToScreen(pos, screenPosVec, viewMatrix, 1280, 720))
        {
            ImDrawList* drawList = ImGui::GetBackgroundDrawList();

            float distance = std::sqrt(pos.x * pos.x + pos.y * pos.y + pos.z * pos.z);
            float scale = 1.0f / (distance * 0.005f);
            float boxWidth = 40.0f * scale;
            float boxHeight = 80.0f * scale;

            ImVec2 topLeft(screenPosVec.x - boxWidth / 2, screenPosVec.y - boxHeight);
            ImVec2 topRight(screenPosVec.x + boxWidth / 2, screenPosVec.y - boxHeight);
            ImVec2 bottomLeft(screenPosVec.x - boxWidth / 2, screenPosVec.y);
            ImVec2 bottomRight(screenPosVec.x + boxWidth / 2, screenPosVec.y);

            ImU32 boxColor = entity.GetTeam() == 1 ? ImGui::ColorConvertFloat4ToU32(config.teamColor) : ImGui::ColorConvertFloat4ToU32(config.enemyColor);
            float thickness = config.boxThickness;
            if (config.showBoxes)
            {
                drawList->AddLine(topLeft, topRight, boxColor, thickness);
                drawList->AddLine(topRight, bottomRight, boxColor, thickness);
                drawList->AddLine(bottomRight, bottomLeft, boxColor, thickness);
                drawList->AddLine(bottomLeft, topLeft, boxColor, thickness);
            }

            float healthBarWidth = 4.0f;
            float healthBarHeight = boxHeight;
            ImVec2 healthBarBgTop(topLeft.x - 6.0f, topLeft.y);
            ImVec2 healthBarBgBottom(healthBarBgTop.x + healthBarWidth, bottomLeft.y);
            if (config.showHealth)
            {
                drawList->AddRectFilled(healthBarBgTop, healthBarBgBottom, IM_COL32(0, 0, 0, 180));
            }

            int health = entity.GetHealth();
            float healthPercent = health / 100.0f;
            ImVec2 healthBarTop(healthBarBgTop.x, healthBarBgTop.y + (healthBarHeight * (1.0f - healthPercent)));
            ImVec2 healthBarBottom(healthBarBgBottom.x, healthBarBgBottom.y);
            ImU32 healthColor = IM_COL32(
                (int)(255 * (1.0f - healthPercent)),
                (int)(255 * healthPercent),
                0,
                255);
            if (config.showHealth)
            {
                drawList->AddRectFilled(healthBarTop, healthBarBottom, healthColor);
            }

            char info[256] = { 0 };
            if (config.showName)
            {
                strcat_s(info, entity.GetName().c_str());
                strcat_s(info, " ");
            }
            if (config.showTeam)
            {
                char teamStr[32];
                sprintf_s(teamStr, "[Team %d] ", entity.GetTeam());
                strcat_s(info, teamStr);
            }
            if (config.showWeapons)
            {
                strcat_s(info, "Weapons: ");
                if (config.showAmmo)
                {
                    char ammoStr[128];
                    sprintf_s(ammoStr, "Pistol:%d Carbine:%d Shotgun:%d ", entity.GetPistolAmmo(), entity.GetCarbineAmmo(), entity.GetShotgunAmmo());
                    strcat_s(info, ammoStr);
                }
            }
            if (config.showState)
            {
                strcat_s(info, "State: Active ");
            }
            ImVec2 textPos(topRight.x + 5, topRight.y);
            drawList->AddText(textPos, ImGui::ColorConvertFloat4ToU32(config.textColor), info);
        }
    }
}

LRESULT CALLBACK WndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    if (ImGui_ImplWin32_WndProcHandler(hWnd, uMsg, wParam, lParam))
        return true;

    return CallWindowProc(g_OriginalWndProc, hWnd, uMsg, wParam, lParam);
}

void MainThread()
{
    if (MH_Initialize() != MH_OK)
    {
        MessageBox(NULL, _T("Failed to initialize MinHook"), _T("Error"), MB_OK | MB_ICONERROR);
        return;
    }

    // Get the address of IDXGISwapChain::Present
    void** vTable = *reinterpret_cast<void***>(g_pSwapChain);
    void* presentAddr = vTable[8];

    if (MH_CreateHook(presentAddr, &hkPresent, reinterpret_cast<void**>(&oPresent)) != MH_OK)
    {
        MessageBox(NULL, _T("Failed to create hook for Present"), _T("Error"), MB_OK | MB_ICONERROR);
        return;
    }

    if (MH_EnableHook(presentAddr) != MH_OK)
    {
        MessageBox(NULL, _T("Failed to enable hook for Present"), _T("Error"), MB_OK | MB_ICONERROR);
        return;
    }

    // Main loop
    while (g_Running)
    {
        Sleep(100);
    }

    MH_DisableHook(presentAddr);
    MH_Uninitialize();
}

DWORD WINAPI ThreadProc(LPVOID lpParameter)
{
    // Wait for the game window to be available
    while (!(g_hGameWindow = FindWindow(NULL, _T("AssaultCube"))) && g_Running)
    {
        Sleep(100);
    }

    // Wait for the device and swapchain to be ready
    // This is a placeholder; in practice, you may need to hook CreateDevice or Present earlier
    Sleep(2000);

    // Hook Present
    HookPresent();

    // Run main thread loop
    MainThread();

    // Cleanup
    CleanupImGui();

    // Restore original WndProc
    if (g_hGameWindow && g_OriginalWndProc)
    {
        SetWindowLongPtr(g_hGameWindow, GWLP_WNDPROC, (LONG_PTR)g_OriginalWndProc);
    }

    return 0;
}

void HookPresent()
{
    // This function is a placeholder; actual hooking is done in MainThread after obtaining swapchain
    // For demonstration, we assume g_pSwapChain is set externally or via hooking CreateDevice
}

void UnhookPresent()
{
    // Unhook Present and cleanup
    MH_DisableHook(MH_ALL_HOOKS);
    MH_Uninitialize();
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);
        g_Thread = std::thread(ThreadProc, nullptr);
        break;
    case DLL_PROCESS_DETACH:
        g_Running = false;
        if (g_Thread.joinable())
            g_Thread.join();
        UnhookPresent();
        break;
    }
    return TRUE;
}
