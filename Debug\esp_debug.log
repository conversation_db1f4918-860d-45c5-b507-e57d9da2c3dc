AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window! Size: 2560x1440
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 -0.017 -0.017]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(175.2,104.7,12.5)->Screen(3233.8,720.0) Visible:N
W2S DEBUG: Foot(175.2,104.7,12.5)->Screen(3233.8,720.0) Visible:N
W2S DEBUG: Head(173.2,106.1,12.5)->Screen(3233.8,720.0) Visible:N
W2S DEBUG: Foot(173.2,106.1,12.5)->Screen(3233.8,720.0) Visible:N
W2S DEBUG: Head(170.3,107.9,12.5)->Screen(3229.6,720.0) Visible:N
W2S DEBUG: Foot(170.3,107.9,12.5)->Screen(3229.6,720.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
W2S DEBUG: Head(166.8,110.3,10.5)->Screen(3229.1,767.8) Visible:N
W2S DEBUG: Foot(166.8,110.3,10.5)->Screen(3229.1,767.8) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
W2S DEBUG: Head(163.1,112.8,5.1)->Screen(3229.0,908.5) Visible:N
W2S DEBUG: Foot(163.1,112.8,5.1)->Screen(3229.0,908.5) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(159.3,115.4,4.5)->Screen(3229.3,933.1) Visible:N
W2S DEBUG: Foot(159.3,115.4,4.5)->Screen(3229.3,933.1) Visible:N
W2S DEBUG: Head(155.6,117.9,4.5)->Screen(-567.4,702.5) Visible:N
W2S DEBUG: Foot(155.6,117.9,4.5)->Screen(-567.4,702.5) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.361 0.237 -0.923 -0.923]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.01, 165.50, 12.50
W2S DEBUG: Head(152.0,120.4,4.5)->Screen(-615.3,671.4) Visible:N
W2S DEBUG: Foot(152.0,120.4,4.5)->Screen(-615.3,671.4) Visible:N
W2S DEBUG: Head(148.3,123.0,4.5)->Screen(-3540.4,176.5) Visible:N
W2S DEBUG: Foot(148.3,123.0,4.5)->Screen(-3540.4,176.5) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(144.7,125.5,4.5)->Screen(459.0,352.8) Visible:Y
W2S DEBUG: Foot(144.7,125.5,4.5)->Screen(459.0,352.8) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 86.89, 163.76, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
W2S DEBUG: Head(141.0,128.0,4.5)->Screen(782.2,372.8) Visible:Y
W2S DEBUG: Foot(141.0,128.0,4.5)->Screen(782.2,372.8) Visible:Y
W2S DEBUG: Head(137.5,130.2,4.5)->Screen(1238.8,354.7) Visible:Y
W2S DEBUG: Foot(137.5,130.2,4.5)->Screen(1238.8,354.7) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(134.4,131.9,4.5)->Screen(1461.2,320.9) Visible:Y
W2S DEBUG: Foot(134.4,131.9,4.5)->Screen(1461.2,320.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.270 -0.043 -0.963 -0.963]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.62, 153.23, 12.50
W2S DEBUG: Head(131.3,133.6,4.5)->Screen(7593.2,1913.0) Visible:N
W2S DEBUG: Foot(131.3,133.6,4.5)->Screen(7593.2,1913.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
W2S DEBUG: Head(128.1,135.3,4.5)->Screen(1906.0,838.2) Visible:Y
W2S DEBUG: Foot(128.1,135.3,4.5)->Screen(1906.0,838.2) Visible:Y
W2S DEBUG: Head(125.3,134.8,4.5)->Screen(1204.2,919.2) Visible:Y
W2S DEBUG: Foot(125.3,134.8,4.5)->Screen(1204.2,919.2) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 101.26, 148.42, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(127.4,131.5,4.5)->Screen(1185.6,897.1) Visible:Y
W2S DEBUG: Foot(127.4,131.5,4.5)->Screen(1185.6,897.1) Visible:Y
W2S DEBUG: Head(130.6,127.1,4.5)->Screen(1148.1,825.3) Visible:Y
W2S DEBUG: Foot(130.6,127.1,4.5)->Screen(1148.1,825.3) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
W2S DEBUG: Head(133.6,121.6,4.5)->Screen(1127.4,724.9) Visible:Y
W2S DEBUG: Foot(133.6,121.6,4.5)->Screen(1127.4,724.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.370 0.165 0.839 0.838]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 101.29, 148.39, 12.50
W2S DEBUG: Head(133.2,117.5,4.5)->Screen(1085.8,671.5) Visible:Y
W2S DEBUG: Foot(133.2,117.5,4.5)->Screen(1085.8,671.5) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(133.9,112.4,4.8)->Screen(1027.1,657.1) Visible:Y
W2S DEBUG: Foot(133.9,112.4,4.8)->Screen(1027.1,657.1) Visible:Y
W2S DEBUG: Head(177.9,160.9,16.5)->Screen(2097.4,462.3) Visible:Y
W2S DEBUG: Foot(177.9,160.9,16.5)->Screen(2097.4,462.3) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 101.29, 148.39, 9.00
W2S DEBUG: Head(178.3,158.4,16.5)->Screen(2045.7,465.7) Visible:Y
W2S DEBUG: Foot(178.3,158.4,16.5)->Screen(2045.7,465.7) Visible:Y
W2S DEBUG: Head(181.2,157.1,16.5)->Screen(2013.3,471.7) Visible:Y
W2S DEBUG: Foot(181.2,157.1,16.5)->Screen(2013.3,471.7) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(182.8,155.1,16.5)->Screen(1975.6,466.7) Visible:Y
W2S DEBUG: Foot(182.8,155.1,16.5)->Screen(1975.6,466.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.372 0.160 0.838 0.837]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 101.29, 148.39, 9.00
W2S DEBUG: Head(184.3,153.7,16.5)->Screen(1946.6,477.8) Visible:Y
W2S DEBUG: Foot(184.3,153.7,16.5)->Screen(1946.6,477.8) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
W2S DEBUG: Head(184.7,154.4,16.5)->Screen(1956.2,479.2) Visible:Y
W2S DEBUG: Foot(184.7,154.4,16.5)->Screen(1956.2,479.2) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(184.1,159.8,19.7)->Screen(2058.9,425.5) Visible:Y
W2S DEBUG: Foot(184.1,159.8,19.7)->Screen(2058.9,425.5) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 101.29, 148.39, 9.00
W2S DEBUG: Head(183.2,163.9,17.9)->Screen(2140.1,446.1) Visible:Y
W2S DEBUG: Foot(183.2,163.9,17.9)->Screen(2140.1,446.1) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
W2S DEBUG: Head(183.1,163.8,16.5)->Screen(2136.4,468.2) Visible:Y
W2S DEBUG: Foot(183.1,163.8,16.5)->Screen(2136.4,468.2) Visible:Y
W2S DEBUG: Head(183.8,160.6,16.5)->Screen(2042.8,458.9) Visible:Y
W2S DEBUG: Foot(183.8,160.6,16.5)->Screen(2042.8,458.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.355 0.183 0.850 0.849]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 101.29, 148.39, 9.00
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(181.6,160.5,16.5)->Screen(2037.0,451.6) Visible:Y
W2S DEBUG: Foot(181.6,160.5,16.5)->Screen(2037.0,451.6) Visible:Y
W2S DEBUG: Head(180.6,164.1,16.5)->Screen(1847.8,466.1) Visible:Y
W2S DEBUG: Foot(180.6,164.1,16.5)->Screen(1847.8,466.1) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
W2S DEBUG: Head(180.2,166.2,16.5)->Screen(1677.4,483.0) Visible:Y
W2S DEBUG: Foot(180.2,166.2,16.5)->Screen(1677.4,483.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 101.29, 148.39, 9.00
W2S DEBUG: Head(177.3,164.3,16.5)->Screen(1653.8,479.8) Visible:Y
W2S DEBUG: Foot(177.3,164.3,16.5)->Screen(1653.8,479.8) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(176.9,161.3,16.5)->Screen(1597.7,480.6) Visible:Y
W2S DEBUG: Foot(176.9,161.3,16.5)->Screen(1597.7,480.6) Visible:Y
W2S DEBUG: Head(173.8,159.3,16.5)->Screen(1577.3,482.7) Visible:Y
W2S DEBUG: Foot(173.8,159.3,16.5)->Screen(1577.3,482.7) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.113 0.184 0.976 0.976]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 101.29, 148.39, 9.00
W2S DEBUG: Head(169.8,159.7,16.5)->Screen(1592.8,476.5) Visible:Y
W2S DEBUG: Foot(169.8,159.7,16.5)->Screen(1592.8,476.5) Visible:Y
W2S DEBUG: Head(170.9,162.3,16.5)->Screen(1199.2,663.0) Visible:Y
W2S DEBUG: Foot(170.9,162.3,16.5)->Screen(1199.2,663.0) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 85.03, 146.70, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.178 0.059 0.984 0.983]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 82.64, 146.32, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:78 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 81.10, 145.10, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:56 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.222 0.100 0.974 0.973]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.37, 141.17, 14.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:-10 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 84.86, 133.38, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.355 0.240 0.925 0.925]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 92.67, 128.80, 16.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-10 T:1 A:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 89.88, 127.54, 16.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-10 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.370 0.233 0.920 0.920]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 89.86, 127.54, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:-10 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 89.44, 130.23, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:-10 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.019 0.240 0.991 0.991]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 89.32, 131.01, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 89.51, 135.18, 16.50
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.05, 139.65, 15.50
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(184.9,105.1,12.5)->Screen(1016.1,574.6) Visible:Y
W2S DEBUG: Foot(184.9,105.1,12.5)->Screen(1016.1,574.6) Visible:Y
W2S DEBUG: Head(184.9,105.1,12.5)->Screen(1016.1,574.6) Visible:Y
W2S DEBUG: Foot(184.9,105.1,12.5)->Screen(1016.1,574.6) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:-4 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(175.2,142.6,16.5)->Screen(1519.0,519.2) Visible:Y
W2S DEBUG: Foot(175.2,142.6,16.5)->Screen(1519.0,519.2) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(173.2,139.4,16.5)->Screen(1469.5,518.9) Visible:Y
W2S DEBUG: Foot(173.2,139.4,16.5)->Screen(1469.5,518.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(173.6,139.8,16.5)->Screen(1475.0,519.0) Visible:Y
W2S DEBUG: Foot(173.6,139.8,16.5)->Screen(1475.0,519.0) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:-4 T:1 A:N
W2S DEBUG: Head(177.8,136.7,16.5)->Screen(1428.7,519.9) Visible:Y
W2S DEBUG: Foot(177.8,136.7,16.5)->Screen(1428.7,519.9) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(178.1,134.5,16.5)->Screen(1395.5,520.0) Visible:Y
W2S DEBUG: Foot(178.1,134.5,16.5)->Screen(1395.5,520.0) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:-4 T:1 A:N
W2S DEBUG: Head(179.0,132.2,16.5)->Screen(1363.7,520.2) Visible:Y
W2S DEBUG: Foot(179.0,132.2,16.5)->Screen(1363.7,520.2) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 1
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:-4 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:-4 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(182.3,163.8,16.5)->Screen(1835.1,520.0) Visible:Y
W2S DEBUG: Foot(182.3,163.8,16.5)->Screen(1835.1,520.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
W2S DEBUG: Head(184.8,164.3,16.5)->Screen(1832.0,520.4) Visible:Y
W2S DEBUG: Foot(184.8,164.3,16.5)->Screen(1832.0,520.4) Visible:Y
W2S DEBUG: Head(184.8,164.0,16.5)->Screen(1828.3,520.4) Visible:Y
W2S DEBUG: Foot(184.8,164.0,16.5)->Screen(1828.3,520.4) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(184.8,163.4,16.5)->Screen(1818.2,520.4) Visible:Y
W2S DEBUG: Foot(184.8,163.4,16.5)->Screen(1818.2,520.4) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:78 T:1 A:Y
W2S DEBUG: Head(184.9,160.8,16.5)->Screen(1779.4,520.5) Visible:Y
W2S DEBUG: Foot(184.9,160.8,16.5)->Screen(1779.4,520.5) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(183.4,155.8,16.5)->Screen(1708.5,520.4) Visible:Y
W2S DEBUG: Foot(183.4,155.8,16.5)->Screen(1708.5,520.4) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:78 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:78 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [4]: 0x10A01B78 - H:78 T:1 A:Y
W2S DEBUG: Head(180.0,154.4,16.5)->Screen(1695.7,519.8) Visible:Y
W2S DEBUG: Foot(180.0,154.4,16.5)->Screen(1695.7,519.8) Visible:Y
W2S DEBUG: Head(177.0,153.2,16.5)->Screen(1684.8,519.3) Visible:Y
W2S DEBUG: Foot(177.0,153.2,16.5)->Screen(1684.8,519.3) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(172.7,153.5,16.5)->Screen(1701.2,518.4) Visible:Y
W2S DEBUG: Foot(172.7,153.5,16.5)->Screen(1701.2,518.4) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(168.3,154.0,16.2)->Screen(1723.5,521.8) Visible:Y
W2S DEBUG: Foot(168.3,154.0,16.2)->Screen(1723.5,521.8) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:78 T:1 A:Y
W2S DEBUG: Head(164.3,155.1,14.2)->Screen(1756.5,557.8) Visible:Y
W2S DEBUG: Foot(164.3,155.1,14.2)->Screen(1756.5,557.8) Visible:Y
W2S DEBUG: Head(163.5,158.1,13.5)->Screen(1817.3,572.2) Visible:Y
W2S DEBUG: Foot(163.5,158.1,13.5)->Screen(1817.3,572.2) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(165.6,160.2,14.5)->Screen(1847.5,553.1) Visible:Y
W2S DEBUG: Foot(165.6,160.2,14.5)->Screen(1847.5,553.1) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:78 T:1 A:Y
W2S DEBUG: Head(167.0,159.2,15.5)->Screen(1823.6,534.8) Visible:Y
W2S DEBUG: Foot(167.0,159.2,15.5)->Screen(1823.6,534.8) Visible:Y
W2S DEBUG: Head(164.5,157.2,14.1)->Screen(1794.8,560.1) Visible:Y
W2S DEBUG: Foot(164.5,157.2,14.1)->Screen(1794.8,560.1) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(160.7,157.3,12.5)->Screen(1813.9,592.9) Visible:Y
W2S DEBUG: Foot(160.7,157.3,12.5)->Screen(1813.9,592.9) Visible:Y
W2S DEBUG: Head(160.7,160.2,12.5)->Screen(1872.7,593.2) Visible:Y
W2S DEBUG: Foot(160.7,160.2,12.5)->Screen(1872.7,593.2) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:78 T:1 A:Y
W2S DEBUG: Head(164.5,161.1,13.5)->Screen(1870.1,571.8) Visible:Y
W2S DEBUG: Foot(164.5,161.1,13.5)->Screen(1870.1,571.8) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(165.7,159.9,14.5)->Screen(1840.8,553.0) Visible:Y
W2S DEBUG: Foot(165.7,159.9,14.5)->Screen(1840.8,553.0) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(162.9,157.6,13.5)->Screen(1809.6,572.8) Visible:Y
W2S DEBUG: Foot(162.9,157.6,13.5)->Screen(1809.6,572.8) Visible:Y
W2S DEBUG: Head(160.6,155.7,12.5)->Screen(1782.3,592.7) Visible:Y
W2S DEBUG: Foot(160.6,155.7,12.5)->Screen(1782.3,592.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [4]: 0x10A01B78 - H:78 T:1 A:Y
W2S DEBUG: Head(158.5,153.5,12.5)->Screen(1745.9,594.3) Visible:Y
W2S DEBUG: Foot(158.5,153.5,12.5)->Screen(1745.9,594.3) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(155.9,156.3,12.5)->Screen(1818.2,597.1) Visible:Y
W2S DEBUG: Foot(155.9,156.3,12.5)->Screen(1818.2,597.1) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [4]: 0x10A01B78 - H:78 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(173.1,166.2,16.5)->Screen(1918.9,518.1) Visible:Y
W2S DEBUG: Foot(173.1,166.2,16.5)->Screen(1918.9,518.1) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
W2S DEBUG: Head(174.7,164.9,16.5)->Screen(1888.1,518.5) Visible:Y
W2S DEBUG: Foot(174.7,164.9,16.5)->Screen(1888.1,518.5) Visible:Y
W2S DEBUG: Head(174.6,162.5,16.5)->Screen(1847.2,518.5) Visible:Y
W2S DEBUG: Foot(174.6,162.5,16.5)->Screen(1847.2,518.5) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(176.4,160.7,16.5)->Screen(1809.6,518.9) Visible:Y
W2S DEBUG: Foot(176.4,160.7,16.5)->Screen(1809.6,518.9) Visible:Y
W2S DEBUG: Head(178.7,159.7,16.5)->Screen(1784.4,519.4) Visible:Y
W2S DEBUG: Foot(178.7,159.7,16.5)->Screen(1784.4,519.4) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
W2S DEBUG: Head(179.6,157.4,16.5)->Screen(1744.5,519.7) Visible:Y
W2S DEBUG: Foot(179.6,157.4,16.5)->Screen(1744.5,519.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(167.3,166.5,15.8)->Screen(1959.5,530.3) Visible:Y
W2S DEBUG: Foot(167.3,166.5,15.8)->Screen(1959.5,530.3) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(165.6,163.8,14.5)->Screen(1917.4,553.2) Visible:Y
W2S DEBUG: Foot(165.6,163.8,14.5)->Screen(1917.4,553.2) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(166.7,159.5,14.5)->Screen(1829.5,552.8) Visible:Y
W2S DEBUG: Foot(166.7,159.5,14.5)->Screen(1829.5,552.8) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(168.5,156.5,15.5)->Screen(1767.9,534.8) Visible:Y
W2S DEBUG: Foot(168.5,156.5,15.5)->Screen(1767.9,534.8) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(166.8,154.4,15.7)->Screen(1736.2,532.1) Visible:Y
W2S DEBUG: Foot(166.8,154.4,15.7)->Screen(1736.2,532.1) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
W2S DEBUG: Head(165.5,158.9,14.5)->Screen(1824.0,553.1) Visible:Y
W2S DEBUG: Foot(165.5,158.9,14.5)->Screen(1824.0,553.1) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(165.8,162.4,14.5)->Screen(1889.3,553.1) Visible:Y
W2S DEBUG: Foot(165.8,162.4,14.5)->Screen(1889.3,553.1) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
W2S DEBUG: Head(168.2,166.7,16.2)->Screen(1958.4,521.4) Visible:Y
W2S DEBUG: Foot(168.2,166.7,16.2)->Screen(1958.4,521.4) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(165.6,164.1,14.5)->Screen(1922.5,553.2) Visible:Y
W2S DEBUG: Foot(165.6,164.1,14.5)->Screen(1922.5,553.2) Visible:Y
W2S DEBUG: Head(166.7,159.4,14.5)->Screen(1828.2,552.8) Visible:Y
W2S DEBUG: Foot(166.7,159.4,14.5)->Screen(1828.2,552.8) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(161.5,165.3,12.8)->Screen(1972.3,588.4) Visible:Y
W2S DEBUG: Foot(161.5,165.3,12.8)->Screen(1972.3,588.4) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(160.0,162.9,12.5)->Screen(1932.1,594.2) Visible:Y
W2S DEBUG: Foot(160.0,162.9,12.5)->Screen(1932.1,594.2) Visible:Y
W2S DEBUG: Head(157.7,160.7,12.5)->Screen(1900.8,595.9) Visible:Y
W2S DEBUG: Foot(157.7,160.7,12.5)->Screen(1900.8,595.9) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
W2S DEBUG: Head(157.2,158.1,12.5)->Screen(1849.9,596.1) Visible:Y
W2S DEBUG: Foot(157.2,158.1,12.5)->Screen(1849.9,596.1) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(157.2,157.5,12.5)->Screen(1836.8,596.0) Visible:Y
W2S DEBUG: Foot(157.2,157.5,12.5)->Screen(1836.8,596.0) Visible:Y
W2S DEBUG: Head(159.3,156.5,12.5)->Screen(1804.4,594.0) Visible:Y
W2S DEBUG: Foot(159.3,156.5,12.5)->Screen(1804.4,594.0) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(161.9,157.0,12.5)->Screen(1801.1,591.8) Visible:Y
W2S DEBUG: Foot(161.9,157.0,12.5)->Screen(1801.1,591.8) Visible:Y
W2S DEBUG: Head(163.9,157.5,13.5)->Screen(1803.4,571.9) Visible:Y
W2S DEBUG: Foot(163.9,157.5,13.5)->Screen(1803.4,571.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
W2S DEBUG: Head(165.6,158.3,14.5)->Screen(1812.3,553.2) Visible:Y
W2S DEBUG: Foot(165.6,158.3,14.5)->Screen(1812.3,553.2) Visible:Y
W2S DEBUG: Head(167.4,159.1,15.2)->Screen(1819.1,541.0) Visible:Y
W2S DEBUG: Foot(167.4,159.1,15.2)->Screen(1819.1,541.0) Visible:Y
W2S DEBUG: Head(169.4,159.6,16.3)->Screen(1820.8,521.2) Visible:Y
W2S DEBUG: Foot(169.4,159.6,16.3)->Screen(1820.8,521.2) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(171.5,160.0,16.5)->Screen(1817.6,517.9) Visible:Y
W2S DEBUG: Foot(171.5,160.0,16.5)->Screen(1817.6,517.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(174.2,160.4,16.5)->Screen(1813.9,518.5) Visible:Y
W2S DEBUG: Foot(174.2,160.4,16.5)->Screen(1813.9,518.5) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
W2S DEBUG: Head(177.3,161.1,16.5)->Screen(1811.6,519.1) Visible:Y
W2S DEBUG: Foot(177.3,161.1,16.5)->Screen(1811.6,519.1) Visible:Y
W2S DEBUG: Head(180.6,161.1,16.5)->Screen(1798.9,519.7) Visible:Y
W2S DEBUG: Foot(180.6,161.1,16.5)->Screen(1798.9,519.7) Visible:Y
W2S DEBUG: Head(183.9,160.9,16.5)->Screen(1783.9,520.3) Visible:Y
W2S DEBUG: Foot(183.9,160.9,16.5)->Screen(1783.9,520.3) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(184.9,160.7,16.5)->Screen(1778.4,520.5) Visible:Y
W2S DEBUG: Foot(184.9,160.7,16.5)->Screen(1778.4,520.5) Visible:Y
W2S DEBUG: Head(184.9,160.4,16.5)->Screen(1773.3,520.5) Visible:Y
W2S DEBUG: Foot(184.9,160.4,16.5)->Screen(1773.3,520.5) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
W2S DEBUG: Head(184.9,160.0,16.5)->Screen(1767.5,520.5) Visible:Y
W2S DEBUG: Foot(184.9,160.0,16.5)->Screen(1767.5,520.5) Visible:Y
W2S DEBUG: Head(184.9,159.7,16.5)->Screen(1762.7,520.5) Visible:Y
W2S DEBUG: Foot(184.9,159.7,16.5)->Screen(1762.7,520.5) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(184.9,159.4,16.5)->Screen(1757.9,520.5) Visible:Y
W2S DEBUG: Foot(184.9,159.4,16.5)->Screen(1757.9,520.5) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(184.9,159.1,16.5)->Screen(1753.4,520.5) Visible:Y
W2S DEBUG: Foot(184.9,159.1,16.5)->Screen(1753.4,520.5) Visible:Y
W2S DEBUG: Head(184.9,158.7,16.5)->Screen(1748.7,520.5) Visible:Y
W2S DEBUG: Foot(184.9,158.7,16.5)->Screen(1748.7,520.5) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
W2S DEBUG: Head(184.9,158.4,16.5)->Screen(1744.0,520.6) Visible:Y
W2S DEBUG: Foot(184.9,158.4,16.5)->Screen(1744.0,520.6) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(184.9,158.1,16.5)->Screen(1739.4,520.6) Visible:Y
W2S DEBUG: Foot(184.9,158.1,16.5)->Screen(1739.4,520.6) Visible:Y
W2S DEBUG: Head(184.9,157.4,16.5)->Screen(1728.5,520.6) Visible:Y
W2S DEBUG: Foot(184.9,157.4,16.5)->Screen(1728.5,520.6) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(184.1,155.4,16.5)->Screen(1701.4,520.5) Visible:Y
W2S DEBUG: Foot(184.1,155.4,16.5)->Screen(1701.4,520.5) Visible:Y
W2S DEBUG: Head(181.4,155.4,16.5)->Screen(1708.3,520.0) Visible:Y
W2S DEBUG: Foot(181.4,155.4,16.5)->Screen(1708.3,520.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
W2S DEBUG: Head(178.9,157.3,16.5)->Screen(1744.5,519.5) Visible:Y
W2S DEBUG: Foot(178.9,157.3,16.5)->Screen(1744.5,519.5) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [4]: 0x10A01B78 - H:27 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(177.0,153.4,16.5)->Screen(1688.3,519.3) Visible:Y
W2S DEBUG: Foot(177.0,153.4,16.5)->Screen(1688.3,519.3) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:-5 T:1 A:N
W2S DEBUG: Head(182.9,152.8,16.5)->Screen(1664.7,520.4) Visible:Y
W2S DEBUG: Foot(182.9,152.8,16.5)->Screen(1664.7,520.4) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(182.9,152.0,16.5)->Screen(1653.8,520.4) Visible:Y
W2S DEBUG: Foot(182.9,152.0,16.5)->Screen(1653.8,520.4) Visible:Y
W2S DEBUG: Head(182.9,151.3,16.5)->Screen(1642.7,520.4) Visible:Y
W2S DEBUG: Foot(182.9,151.3,16.5)->Screen(1642.7,520.4) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:-5 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
W2S DEBUG: Head(180.7,148.9,16.5)->Screen(1610.0,520.1) Visible:Y
W2S DEBUG: Foot(180.7,148.9,16.5)->Screen(1610.0,520.1) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.4,148.7,16.5)->Screen(1617.7,518.9) Visible:Y
W2S DEBUG: Foot(174.4,148.7,16.5)->Screen(1617.7,518.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.147 0.251 0.979 0.979]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.06, 139.66, 15.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-5 T:1 A:N
W2S DEBUG: Head(172.8,154.5,17.8)->Screen(1719.2,497.1) Visible:Y
W2S DEBUG: Foot(172.8,154.5,17.8)->Screen(1719.2,497.1) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(172.2,161.1,19.3)->Screen(1837.7,470.5) Visible:Y
W2S DEBUG: Foot(172.2,161.1,19.3)->Screen(1837.7,470.5) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 89.68, 139.71, 15.50
W2S DEBUG: Head(170.0,165.8,16.5)->Screen(11348.0,612.6) Visible:N
W2S DEBUG: Foot(170.0,165.8,16.5)->Screen(11348.0,612.6) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-5 T:1 A:N
W2S DEBUG: Head(169.4,162.7,16.5)->Screen(1947.8,630.3) Visible:Y
W2S DEBUG: Foot(169.4,162.7,16.5)->Screen(1947.8,630.3) Visible:Y
W2S DEBUG: Head(167.6,160.4,15.5)->Screen(1338.3,617.8) Visible:Y
W2S DEBUG: Foot(167.6,160.4,15.5)->Screen(1338.3,617.8) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.344 0.145 0.936 0.936]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 92.25, 132.26, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(164.6,160.4,14.0)->Screen(1267.7,650.6) Visible:Y
W2S DEBUG: Foot(164.6,160.4,14.0)->Screen(1267.7,650.6) Visible:Y
W2S DEBUG: Head(166.2,160.9,14.5)->Screen(1261.9,709.6) Visible:Y
W2S DEBUG: Foot(166.2,160.9,14.5)->Screen(1261.9,709.6) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:-5 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.94, 129.02, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.363 0.055 0.932 0.931]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 88.64, 130.83, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 85.56, 128.73, 16.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.323 0.090 0.885 0.884]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 83.42, 128.17, 16.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 83.42, 128.17, 13.00
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.417 -0.027 0.803 0.803]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 83.42, 128.17, 13.00
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:34 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 81.83, 135.95, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.239 0.167 0.967 0.967]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 83.08, 126.94, 16.50
ENTITY DEBUG [4]: 0x10A01B78 - H:34 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 84.04, 123.81, 16.50
ENTITY DEBUG [4]: 0x10A01B78 - H:32 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.777 0.103 0.627 0.627]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.13, 116.65, 14.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:-84 T:1 A:N
W2S DEBUG: Head(174.0,163.8,16.5)->Screen(1267.5,534.4) Visible:Y
W2S DEBUG: Foot(174.0,163.8,16.5)->Screen(1267.5,534.4) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 85.08, 116.17, 14.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(177.4,161.1,16.5)->Screen(1361.3,584.5) Visible:Y
W2S DEBUG: Foot(177.4,161.1,16.5)->Screen(1361.3,584.5) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:-84 T:1 A:N
W2S DEBUG: Head(179.9,157.4,16.5)->Screen(1330.2,613.6) Visible:Y
W2S DEBUG: Foot(179.9,157.4,16.5)->Screen(1330.2,613.6) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.271 0.141 0.960 0.959]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 88.13, 125.74, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(183.6,154.3,16.5)->Screen(1409.2,615.9) Visible:Y
W2S DEBUG: Foot(183.6,154.3,16.5)->Screen(1409.2,615.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 1
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 88.82, 132.80, 16.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-84 T:1 A:N
W2S DEBUG: Head(182.6,163.5,16.5)->Screen(2490.6,410.4) Visible:Y
W2S DEBUG: Foot(182.6,163.5,16.5)->Screen(2490.6,410.4) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.332 0.240 0.934 0.934]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 1
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 92.29, 135.00, 16.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-84 T:1 A:N
W2S DEBUG: Head(176.5,163.0,16.5)->Screen(1348.2,636.7) Visible:Y
W2S DEBUG: Foot(176.5,163.0,16.5)->Screen(1348.2,636.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 1
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 92.43, 125.48, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(177.7,153.9,16.5)->Screen(1141.4,677.5) Visible:Y
W2S DEBUG: Foot(177.7,153.9,16.5)->Screen(1141.4,677.5) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:-84 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.222 0.057 0.975 0.975]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 1
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.89, 128.91, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(173.4,158.0,16.5)->Screen(1270.6,675.5) Visible:Y
W2S DEBUG: Foot(173.4,158.0,16.5)->Screen(1270.6,675.5) Visible:Y
W2S DEBUG: Head(176.0,159.8,16.5)->Screen(1261.4,671.4) Visible:Y
W2S DEBUG: Foot(176.0,159.8,16.5)->Screen(1261.4,671.4) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 90.79, 128.95, 16.50
W2S DEBUG: Head(177.9,158.7,16.5)->Screen(1199.4,677.5) Visible:Y
W2S DEBUG: Foot(177.9,158.7,16.5)->Screen(1199.4,677.5) Visible:Y
W2S DEBUG: Head(181.2,157.8,16.5)->Screen(1163.2,667.3) Visible:Y
W2S DEBUG: Foot(181.2,157.8,16.5)->Screen(1163.2,667.3) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(183.8,156.8,16.5)->Screen(1103.8,669.4) Visible:Y
W2S DEBUG: Foot(183.8,156.8,16.5)->Screen(1103.8,669.4) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.438 0.106 0.897 0.897]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 86.30, 127.94, 16.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
W2S DEBUG: Head(184.3,160.0,16.5)->Screen(1073.3,671.3) Visible:Y
W2S DEBUG: Foot(184.3,160.0,16.5)->Screen(1073.3,671.3) Visible:Y
W2S DEBUG: Head(162.7,146.5,5.0)->Screen(894.7,848.8) Visible:Y
W2S DEBUG: Foot(162.7,146.5,5.0)->Screen(894.7,848.8) Visible:Y
W2S DEBUG: Head(161.1,148.5,4.6)->Screen(937.6,802.2) Visible:Y
W2S DEBUG: Foot(161.1,148.5,4.6)->Screen(937.6,802.2) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(158.5,148.9,5.5)->Screen(953.1,794.0) Visible:Y
W2S DEBUG: Foot(158.5,148.9,5.5)->Screen(953.1,794.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 85.38, 118.64, 14.50
W2S DEBUG: Head(155.6,148.9,5.5)->Screen(970.9,811.6) Visible:Y
W2S DEBUG: Foot(155.6,148.9,5.5)->Screen(970.9,811.6) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
W2S DEBUG: Head(152.7,149.1,4.5)->Screen(987.3,827.0) Visible:Y
W2S DEBUG: Foot(152.7,149.1,4.5)->Screen(987.3,827.0) Visible:Y
W2S DEBUG: Head(149.8,150.9,4.5)->Screen(1023.6,828.9) Visible:Y
W2S DEBUG: Foot(149.8,150.9,4.5)->Screen(1023.6,828.9) Visible:Y
W2S DEBUG: Head(146.6,150.9,4.5)->Screen(1040.0,838.0) Visible:Y
W2S DEBUG: Foot(146.6,150.9,4.5)->Screen(1040.0,838.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.461 0.022 0.753 0.752]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 85.34, 118.50, 14.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(143.3,150.9,4.5)->Screen(1052.5,846.4) Visible:Y
W2S DEBUG: Foot(143.3,150.9,4.5)->Screen(1052.5,846.4) Visible:Y
W2S DEBUG: Head(140.0,150.9,4.5)->Screen(940.4,982.5) Visible:Y
W2S DEBUG: Foot(140.0,150.9,4.5)->Screen(940.4,982.5) Visible:Y
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
W2S DEBUG: Head(136.6,150.9,4.5)->Screen(908.8,984.5) Visible:Y
W2S DEBUG: Foot(136.6,150.9,4.5)->Screen(908.8,984.5) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 85.34, 118.50, 14.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.700 -0.112 0.711 0.711]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 103.26, 98.25, 5.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 109.38, 108.37, 4.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.526 -0.069 0.850 0.850]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 107.45, 124.18, 4.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 111.88, 137.09, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.882 -0.056 0.470 0.470]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 126.27, 141.03, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 130.99, 151.90, 7.15
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.868 -0.055 -0.495 -0.495]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 135.27, 155.58, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:56 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 150.90, 153.55, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-10 T:1 A:N
W2S DEBUG: Head(157.1,166.9,12.5)->Screen(3789.6,721.3) Visible:N
W2S DEBUG: Foot(157.1,166.9,12.5)->Screen(3789.6,721.3) Visible:N
W2S DEBUG: Head(157.1,166.9,12.5)->Screen(4425.6,793.7) Visible:N
W2S DEBUG: Foot(157.1,166.9,12.5)->Screen(4425.6,793.7) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.081 -0.220 -0.989 -0.989]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 152.30, 147.48, 9.47
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(159.4,164.8,12.5)->Screen(3914.9,1301.1) Visible:N
W2S DEBUG: Foot(159.4,164.8,12.5)->Screen(3914.9,1301.1) Visible:N
W2S DEBUG: Head(162.4,161.0,12.5)->Screen(3064.3,1468.2) Visible:N
W2S DEBUG: Foot(162.4,161.0,12.5)->Screen(3064.3,1468.2) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-10 T:1 A:N
