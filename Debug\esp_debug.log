AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window! Size: 2560x1440
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.654 -0.252 -0.296 -0.296]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 96.93, 134.90, 16.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(169.3,134.8,1.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(169.3,134.8,1.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 123.00, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.167 0.148 0.983 0.982]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 123.00, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 119.81, 122.61, 4.50
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.016 0.102 0.997 0.996]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 133.55, 130.71, 4.50
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 133.58, 130.78, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.020 0.102 0.997 0.996]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 133.58, 130.78, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 97.00, 165.00, 12.50
W2S DEBUG: Head(154.1,131.0,3.3)->Screen(-33147.4,7002.1) Visible:N
W2S DEBUG: Foot(154.1,131.0,3.3)->Screen(-33147.4,7002.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(153.1,131.8,0.6)->Screen(-72937.4,17248.0) Visible:N
W2S DEBUG: Foot(153.1,131.8,0.6)->Screen(-72937.4,17248.0) Visible:N
W2S DEBUG: Head(153.1,132.0,-1.0)->Screen(-189420.5,47653.4) Visible:N
W2S DEBUG: Foot(153.1,132.0,-1.0)->Screen(-189420.5,47653.4) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.790 -0.547 0.530 0.530]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 97.00, 165.00, 12.50
W2S DEBUG: Head(153.1,132.0,0.6)->Screen(-63990.2,15346.3) Visible:N
W2S DEBUG: Foot(153.1,132.0,0.6)->Screen(-63990.2,15346.3) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
W2S DEBUG: Head(153.1,132.1,1.4)->Screen(-46778.6,10998.4) Visible:N
W2S DEBUG: Foot(153.1,132.1,1.4)->Screen(-46778.6,10998.4) Visible:N
W2S DEBUG: Head(153.1,132.5,1.0)->Screen(-44508.9,10917.4) Visible:N
W2S DEBUG: Foot(153.1,132.5,1.0)->Screen(-44508.9,10917.4) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(153.1,133.0,-0.0)->Screen(-49251.6,12930.1) Visible:N
W2S DEBUG: Foot(153.1,133.0,-0.0)->Screen(-49251.6,12930.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 96.92, 165.01, 12.50
W2S DEBUG: Head(152.6,133.8,1.1)->Screen(-31353.1,8278.7) Visible:N
W2S DEBUG: Foot(152.6,133.8,1.1)->Screen(-31353.1,8278.7) Visible:N
W2S DEBUG: Head(151.5,134.8,2.6)->Screen(-21399.4,5644.1) Visible:N
W2S DEBUG: Foot(151.5,134.8,2.6)->Screen(-21399.4,5644.1) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
W2S DEBUG: Head(150.2,135.6,2.2)->Screen(-22638.8,6140.2) Visible:N
W2S DEBUG: Foot(150.2,135.6,2.2)->Screen(-22638.8,6140.2) Visible:N
W2S DEBUG: Head(148.9,136.2,1.5)->Screen(-27914.7,7731.2) Visible:N
W2S DEBUG: Foot(148.9,136.2,1.5)->Screen(-27914.7,7731.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.790 -0.547 0.530 0.530]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 96.35, 165.09, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(147.5,136.4,1.5)->Screen(-34580.1,9307.7) Visible:N
W2S DEBUG: Foot(147.5,136.4,1.5)->Screen(-34580.1,9307.7) Visible:N
W2S DEBUG: Head(146.6,136.0,2.5)->Screen(-38055.3,9335.2) Visible:N
W2S DEBUG: Foot(146.6,136.0,2.5)->Screen(-38055.3,9335.2) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
W2S DEBUG: Head(144.8,135.7,2.9)->Screen(-2650.4,1386.1) Visible:N
W2S DEBUG: Foot(144.8,135.7,2.9)->Screen(-2650.4,1386.1) Visible:N
W2S DEBUG: Head(142.6,136.4,4.2)->Screen(-2741.6,1330.2) Visible:N
W2S DEBUG: Foot(142.6,136.4,4.2)->Screen(-2741.6,1330.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.89, 165.79, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(141.0,137.1,4.5)->Screen(-2744.9,1328.9) Visible:N
W2S DEBUG: Foot(141.0,137.1,4.5)->Screen(-2744.9,1328.9) Visible:N
W2S DEBUG: Head(139.0,137.8,4.5)->Screen(-2834.4,1366.8) Visible:N
W2S DEBUG: Foot(139.0,137.8,4.5)->Screen(-2834.4,1366.8) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
W2S DEBUG: Head(137.8,138.1,4.5)->Screen(-2952.9,1400.4) Visible:N
W2S DEBUG: Foot(137.8,138.1,4.5)->Screen(-2952.9,1400.4) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(135.2,138.1,4.5)->Screen(-3486.9,1519.1) Visible:N
W2S DEBUG: Foot(135.2,138.1,4.5)->Screen(-3486.9,1519.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(166.8,142.9,1.5)->Screen(-666.0,1027.1) Visible:N
W2S DEBUG: Foot(166.8,142.9,1.5)->Screen(-666.0,1027.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:18 T:1 A:Y
W2S DEBUG: Head(166.5,142.9,1.5)->Screen(-670.9,1029.0) Visible:N
W2S DEBUG: Foot(166.5,142.9,1.5)->Screen(-670.9,1029.0) Visible:N
W2S DEBUG: Head(166.4,142.9,1.5)->Screen(-672.3,1029.6) Visible:N
W2S DEBUG: Foot(166.4,142.9,1.5)->Screen(-672.3,1029.6) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(166.6,142.9,1.5)->Screen(-668.3,1028.0) Visible:N
W2S DEBUG: Foot(166.6,142.9,1.5)->Screen(-668.3,1028.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:18 T:1 A:Y
W2S DEBUG: Head(166.9,142.9,1.5)->Screen(-664.2,1026.4) Visible:N
W2S DEBUG: Foot(166.9,142.9,1.5)->Screen(-664.2,1026.4) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(133.9,151.8,5.2)->Screen(-526.8,1064.1) Visible:N
W2S DEBUG: Foot(133.9,151.8,5.2)->Screen(-526.8,1064.1) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 1
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(133.7,145.5,4.5)->Screen(-1437.3,1237.9) Visible:N
W2S DEBUG: Foot(133.7,145.5,4.5)->Screen(-1437.3,1237.9) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 1
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(138.0,147.8,4.5)->Screen(-868.3,1110.7) Visible:N
W2S DEBUG: Foot(138.0,147.8,4.5)->Screen(-868.3,1110.7) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 1
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
W2S DEBUG: Head(132.2,151.7,5.0)->Screen(-575.3,1098.1) Visible:N
W2S DEBUG: Foot(132.2,151.7,5.0)->Screen(-575.3,1098.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 1
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
W2S DEBUG: Head(130.1,151.9,5.4)->Screen(-604.8,1098.8) Visible:N
W2S DEBUG: Foot(130.1,151.9,5.4)->Screen(-604.8,1098.8) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 1
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(169.8,90.7,12.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(169.8,90.7,12.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
W2S DEBUG: Head(171.3,88.2,12.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(171.3,88.2,12.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.3,86.7,12.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.3,86.7,12.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(177.6,85.7,12.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(177.6,85.7,12.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(181.6,84.5,12.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(181.6,84.5,12.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-42 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(106.4,108.3,7.4)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(106.4,108.3,7.4)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(106.2,109.2,7.1)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(106.2,109.2,7.1)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(105.8,109.9,5.8)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(105.8,109.9,5.8)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(106.0,109.9,4.6)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(106.0,109.9,4.6)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(106.6,109.8,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(106.6,109.8,4.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
W2S DEBUG: Head(107.5,108.5,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(107.5,108.5,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(108.3,106.7,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(108.3,106.7,4.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(109.3,104.8,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(109.3,104.8,4.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(110.7,103.2,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(110.7,103.2,4.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
W2S DEBUG: Head(112.7,102.1,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(112.7,102.1,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(114.7,101.8,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(114.7,101.8,4.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(116.2,102.9,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(116.2,102.9,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(115.8,105.1,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(115.8,105.1,4.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
W2S DEBUG: Head(113.8,107.0,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(113.8,107.0,4.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(111.3,108.5,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(111.3,108.5,4.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(110.5,107.7,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(110.5,107.7,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(110.3,105.5,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(110.3,105.5,4.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(111.3,103.3,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(111.3,103.3,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(110.7,101.4,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(110.7,101.4,4.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(109.1,100.4,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(109.1,100.4,4.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
W2S DEBUG: Head(107.1,100.0,4.6)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(107.1,100.0,4.6)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(104.9,99.6,4.7)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(104.9,99.6,4.7)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(102.8,98.7,5.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(102.8,98.7,5.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(101.3,97.1,5.9)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(101.3,97.1,5.9)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
W2S DEBUG: Head(101.1,97.1,8.1)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(101.1,97.1,8.1)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(101.1,97.1,8.7)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(101.1,97.1,8.7)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(101.1,97.1,8.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(101.1,97.1,8.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(101.4,97.1,6.3)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(101.4,97.1,6.3)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:40 T:1 A:Y
W2S DEBUG: Head(102.1,97.1,7.4)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(102.1,97.1,7.4)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(103.4,97.3,8.4)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(103.4,97.3,8.4)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(104.1,98.4,6.4)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(104.1,98.4,6.4)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-20 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(103.4,100.1,5.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(103.4,100.1,5.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(101.5,100.9,9.4)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(101.5,100.9,9.4)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 2
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-20 T:1 A:N
W2S DEBUG: Head(101.1,101.7,13.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(101.1,101.7,13.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(101.2,103.5,14.7)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(101.2,103.5,14.7)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
W2S DEBUG: Head(101.7,105.0,15.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(101.7,105.0,15.0)->Screen(0.0,0.0) Visible:N
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(102.6,106.2,14.3)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(102.6,106.2,14.3)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-20 T:1 A:N
W2S DEBUG: Head(103.6,107.5,12.6)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(103.6,107.5,12.6)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
W2S DEBUG: Head(104.8,108.9,9.9)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(104.8,108.9,9.9)->Screen(0.0,0.0) Visible:N
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(106.0,109.8,6.4)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(106.0,109.8,6.4)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-20 T:1 A:N
W2S DEBUG: Head(107.3,109.8,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(107.3,109.8,4.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
W2S DEBUG: Head(108.5,108.5,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(108.5,108.5,4.5)->Screen(0.0,0.0) Visible:N
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(109.7,106.8,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(109.7,106.8,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(111.3,105.5,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(111.3,105.5,4.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-20 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
W2S DEBUG: Head(113.3,104.8,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(113.3,104.8,4.5)->Screen(0.0,0.0) Visible:N
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(115.6,104.5,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(115.6,104.5,4.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(117.9,104.5,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(117.9,104.5,4.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
W2S DEBUG: Head(118.8,104.7,6.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(118.8,104.7,6.5)->Screen(0.0,0.0) Visible:N
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:-20 T:1 A:N
W2S DEBUG: Head(119.6,104.9,9.1)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(119.6,104.9,9.1)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(121.9,104.9,10.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(121.9,104.9,10.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
W2S DEBUG: Head(124.3,104.9,10.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(124.3,104.9,10.5)->Screen(0.0,0.0) Visible:N
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(126.4,104.7,9.3)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(126.4,104.7,9.3)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-20 T:1 A:N
W2S DEBUG: Head(128.2,103.9,8.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(128.2,103.9,8.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
W2S DEBUG: Head(129.1,103.1,8.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(129.1,103.1,8.5)->Screen(0.0,0.0) Visible:N
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(129.5,102.4,9.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(129.5,102.4,9.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-20 T:1 A:N
W2S DEBUG: Head(129.8,100.7,9.9)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(129.8,100.7,9.9)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
W2S DEBUG: Head(130.0,99.2,10.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(130.0,99.2,10.5)->Screen(0.0,0.0) Visible:N
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(130.2,98.3,11.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(130.2,98.3,11.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(130.2,96.6,12.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(130.2,96.6,12.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x10A01B78 - H:-20 T:1 A:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
W2S DEBUG: Head(130.1,95.1,12.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(130.1,95.1,12.5)->Screen(0.0,0.0) Visible:N
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.553 0.000 0.613 0.613]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 3
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 94.84, 165.82, 12.50
W2S DEBUG: Head(130.9,93.9,12.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(130.9,93.9,12.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
