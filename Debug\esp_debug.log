AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window! Size: 2560x1440
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.951 -0.015 -0.311 -0.310]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 85.00, 188.00, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(163.5,145.4,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(163.5,145.4,7.5)->Screen(0.0,0.0) Visible:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(162.7,147.5,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(162.7,147.5,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(162.0,149.6,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(162.0,149.6,7.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(161.3,151.7,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(161.3,151.7,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(160.7,153.8,7.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(160.7,153.8,7.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(160.1,156.0,5.9)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(160.1,156.0,5.9)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 85.08, 187.76, 4.50
W2S DEBUG: Head(159.9,158.1,4.8)->Screen(-908.0,878.3) Visible:N
W2S DEBUG: Foot(159.9,158.1,4.8)->Screen(-908.0,878.3) Visible:N
W2S DEBUG: Head(160.6,159.4,4.5)->Screen(-807.3,878.5) Visible:N
W2S DEBUG: Foot(160.6,159.4,4.5)->Screen(-807.3,878.5) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(161.7,158.8,4.5)->Screen(-2959.7,777.6) Visible:N
W2S DEBUG: Foot(161.7,158.8,4.5)->Screen(-2959.7,777.6) Visible:N
W2S DEBUG: Head(161.6,157.2,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(161.6,157.2,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(160.9,155.2,5.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(160.9,155.2,5.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(160.1,153.3,6.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(160.1,153.3,6.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.783 -0.049 -0.621 -0.621]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 84.82, 188.10, 4.50
W2S DEBUG: Head(159.6,152.2,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(159.6,152.2,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(160.8,151.3,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(160.8,151.3,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(162.7,149.4,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(162.7,149.4,7.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(163.9,147.4,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(163.9,147.4,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(164.4,144.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(164.4,144.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(165.5,143.0,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(165.5,143.0,7.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 76.03, 194.19, 4.50
W2S DEBUG: Head(167.3,141.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(167.3,141.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(169.4,141.5,7.5)->Screen(9295.7,385.3) Visible:N
W2S DEBUG: Foot(169.4,141.5,7.5)->Screen(9295.7,385.3) Visible:N
W2S DEBUG: Head(171.6,141.5,7.5)->Screen(9025.9,397.2) Visible:N
W2S DEBUG: Foot(171.6,141.5,7.5)->Screen(9025.9,397.2) Visible:N
W2S DEBUG: Head(173.7,141.8,7.5)->Screen(8084.6,449.6) Visible:N
W2S DEBUG: Foot(173.7,141.8,7.5)->Screen(8084.6,449.6) Visible:N
W2S DEBUG: Head(175.9,142.1,7.5)->Screen(5847.8,498.0) Visible:N
W2S DEBUG: Foot(175.9,142.1,7.5)->Screen(5847.8,498.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(178.2,142.4,7.5)->Screen(6057.7,494.6) Visible:N
W2S DEBUG: Foot(178.2,142.4,7.5)->Screen(6057.7,494.6) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.973 -0.030 -0.231 -0.230]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 77.85, 196.90, 4.50
W2S DEBUG: Head(180.4,142.7,7.5)->Screen(6287.2,490.8) Visible:N
W2S DEBUG: Foot(180.4,142.7,7.5)->Screen(6287.2,490.8) Visible:N
W2S DEBUG: Head(182.5,143.1,7.5)->Screen(6523.9,486.9) Visible:N
W2S DEBUG: Foot(182.5,143.1,7.5)->Screen(6523.9,486.9) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(184.7,143.3,7.5)->Screen(6769.0,483.0) Visible:N
W2S DEBUG: Foot(184.7,143.3,7.5)->Screen(6769.0,483.0) Visible:N
W2S DEBUG: Head(186.8,142.8,7.5)->Screen(6952.5,480.9) Visible:N
W2S DEBUG: Foot(186.8,142.8,7.5)->Screen(6952.5,480.9) Visible:N
W2S DEBUG: Head(188.5,141.5,8.2)->Screen(7240.0,441.7) Visible:N
W2S DEBUG: Foot(188.5,141.5,8.2)->Screen(7240.0,441.7) Visible:N
W2S DEBUG: Head(190.7,141.1,10.2)->Screen(7985.3,300.4) Visible:N
W2S DEBUG: Foot(190.7,141.1,10.2)->Screen(7985.3,300.4) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 75.57, 190.93, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(192.8,139.9,10.7)->Screen(8630.1,246.7) Visible:N
W2S DEBUG: Foot(192.8,139.9,10.7)->Screen(8630.1,246.7) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(194.7,138.3,10.1)->Screen(9201.5,277.0) Visible:N
W2S DEBUG: Foot(194.7,138.3,10.1)->Screen(9201.5,277.0) Visible:N
W2S DEBUG: Head(196.3,136.5,8.3)->Screen(9646.4,384.7) Visible:N
W2S DEBUG: Foot(196.3,136.5,8.3)->Screen(9646.4,384.7) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(196.9,134.9,9.1)->Screen(10152.5,321.2) Visible:N
W2S DEBUG: Foot(196.9,134.9,9.1)->Screen(10152.5,321.2) Visible:N
W2S DEBUG: Head(196.7,133.3,10.3)->Screen(9169.1,283.1) Visible:N
W2S DEBUG: Foot(196.7,133.3,10.3)->Screen(9169.1,283.1) Visible:N
W2S DEBUG: Head(196.0,131.8,10.2)->Screen(9371.2,280.0) Visible:N
W2S DEBUG: Foot(196.0,131.8,10.2)->Screen(9371.2,280.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.028 -0.206 -0.206]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 66.45, 179.45, 4.50
W2S DEBUG: Head(195.0,130.3,9.0)->Screen(9515.2,348.2) Visible:N
W2S DEBUG: Foot(195.0,130.3,9.0)->Screen(9515.2,348.2) Visible:N
W2S DEBUG: Head(193.8,128.9,7.5)->Screen(9480.0,437.6) Visible:N
W2S DEBUG: Foot(193.8,128.9,7.5)->Screen(9480.0,437.6) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(192.0,128.2,7.5)->Screen(9232.4,441.6) Visible:N
W2S DEBUG: Foot(192.0,128.2,7.5)->Screen(9232.4,441.6) Visible:N
W2S DEBUG: Head(191.1,129.5,7.5)->Screen(9652.3,430.5) Visible:N
W2S DEBUG: Foot(191.1,129.5,7.5)->Screen(9652.3,430.5) Visible:N
W2S DEBUG: Head(190.6,131.9,7.5)->Screen(10655.7,406.0) Visible:N
W2S DEBUG: Foot(190.6,131.9,7.5)->Screen(10655.7,406.0) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(189.2,133.6,7.5)->Screen(11301.7,388.2) Visible:N
W2S DEBUG: Foot(189.2,133.6,7.5)->Screen(11301.7,388.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(186.9,133.4,7.5)->Screen(10743.8,397.3) Visible:N
W2S DEBUG: Foot(186.9,133.4,7.5)->Screen(10743.8,397.3) Visible:N
W2S DEBUG: Head(184.4,132.1,8.8)->Screen(9754.8,329.3) Visible:N
W2S DEBUG: Foot(184.4,132.1,8.8)->Screen(9754.8,329.3) Visible:N
W2S DEBUG: Head(182.0,131.5,9.0)->Screen(9161.2,331.8) Visible:N
W2S DEBUG: Foot(182.0,131.5,9.0)->Screen(9161.2,331.8) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(179.8,131.3,7.5)->Screen(8760.6,432.4) Visible:N
W2S DEBUG: Foot(179.8,131.3,7.5)->Screen(8760.6,432.4) Visible:N
W2S DEBUG: Head(177.8,131.1,7.5)->Screen(8422.1,440.2) Visible:N
W2S DEBUG: Foot(177.8,131.1,7.5)->Screen(8422.1,440.2) Visible:N
W2S DEBUG: Head(177.1,131.1,7.5)->Screen(8334.2,441.4) Visible:N
W2S DEBUG: Foot(177.1,131.1,7.5)->Screen(8334.2,441.4) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.028 -0.206 -0.206]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(177.2,131.1,7.5)->Screen(8353.0,441.1) Visible:N
W2S DEBUG: Foot(177.2,131.1,7.5)->Screen(8353.0,441.1) Visible:N
W2S DEBUG: Head(177.6,131.1,7.5)->Screen(8403.6,440.4) Visible:N
W2S DEBUG: Foot(177.6,131.1,7.5)->Screen(8403.6,440.4) Visible:N
W2S DEBUG: Head(178.1,131.1,7.5)->Screen(8460.7,439.6) Visible:N
W2S DEBUG: Foot(178.1,131.1,7.5)->Screen(8460.7,439.6) Visible:N
W2S DEBUG: Head(178.2,131.1,7.5)->Screen(8480.7,439.4) Visible:N
W2S DEBUG: Foot(178.2,131.1,7.5)->Screen(8480.7,439.4) Visible:N
W2S DEBUG: Head(177.4,131.1,7.5)->Screen(8373.0,440.8) Visible:N
W2S DEBUG: Foot(177.4,131.1,7.5)->Screen(8373.0,440.8) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(175.8,131.1,7.5)->Screen(8170.8,443.6) Visible:N
W2S DEBUG: Foot(175.8,131.1,7.5)->Screen(8170.8,443.6) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(7970.4,446.4) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(7970.4,446.4) Visible:N
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(7970.4,446.4) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(7970.4,446.4) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(7970.4,446.4) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(7970.4,446.4) Visible:N
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(7970.4,446.4) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(7970.4,446.4) Visible:N
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(7972.3,446.4) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(7972.3,446.4) Visible:N
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(7976.4,446.3) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(7976.4,446.3) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.028 -0.206 -0.206]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(7972.2,446.4) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(7972.2,446.4) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.028 -0.206 -0.206]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(7964.7,446.5) Visible:N
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(8088.8,443.4) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(8088.8,443.4) Visible:N
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(8093.7,443.4) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(8093.7,443.4) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.029 -0.209 -0.209]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.029 -0.209 -0.209]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8079.9,443.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8079.9,443.6) Visible:N
W2S DEBUG: Head(174.1,131.2,7.5)->Screen(8103.0,443.0) Visible:N
W2S DEBUG: Foot(174.1,131.2,7.5)->Screen(8103.0,443.0) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.029 -0.209 -0.209]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.1,131.4,7.5)->Screen(8156.8,441.5) Visible:N
W2S DEBUG: Foot(174.1,131.4,7.5)->Screen(8156.8,441.5) Visible:N
W2S DEBUG: Head(174.1,131.6,7.5)->Screen(8229.3,439.5) Visible:N
W2S DEBUG: Foot(174.1,131.6,7.5)->Screen(8229.3,439.5) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(8310.6,437.3) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(8310.6,437.3) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(8320.0,437.1) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(8320.0,437.1) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(8318.7,437.1) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(8318.7,437.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.5,7.5)->Screen(8207.3,440.1) Visible:N
W2S DEBUG: Foot(174.1,131.5,7.5)->Screen(8207.3,440.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8092.5,443.2) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8092.5,443.2) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8092.5,443.2) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8092.5,443.2) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8093.6,443.2) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8093.6,443.2) Visible:N
W2S DEBUG: Head(174.3,131.1,7.5)->Screen(8111.2,443.0) Visible:N
W2S DEBUG: Foot(174.3,131.1,7.5)->Screen(8111.2,443.0) Visible:N
W2S DEBUG: Head(174.4,131.1,7.5)->Screen(8125.0,442.8) Visible:N
W2S DEBUG: Foot(174.4,131.1,7.5)->Screen(8125.0,442.8) Visible:N
W2S DEBUG: Head(174.4,131.1,7.5)->Screen(8130.2,442.7) Visible:N
W2S DEBUG: Foot(174.4,131.1,7.5)->Screen(8130.2,442.7) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.029 -0.209 -0.209]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.5,131.1,7.5)->Screen(8144.3,442.5) Visible:N
W2S DEBUG: Foot(174.5,131.1,7.5)->Screen(8144.3,442.5) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(174.3,131.1,7.5)->Screen(8117.6,442.9) Visible:N
W2S DEBUG: Foot(174.3,131.1,7.5)->Screen(8117.6,442.9) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.6) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.7,443.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.7,443.6) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8081.5,443.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8081.5,443.6) Visible:N
W2S DEBUG: Head(174.1,131.2,7.5)->Screen(8106.4,442.9) Visible:N
W2S DEBUG: Foot(174.1,131.2,7.5)->Screen(8106.4,442.9) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.1,131.4,7.5)->Screen(8158.3,441.5) Visible:N
W2S DEBUG: Foot(174.1,131.4,7.5)->Screen(8158.3,441.5) Visible:N
W2S DEBUG: Head(174.1,131.6,7.5)->Screen(8239.3,439.3) Visible:N
W2S DEBUG: Foot(174.1,131.6,7.5)->Screen(8239.3,439.3) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(8322.2,437.0) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(8322.2,437.0) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(8322.2,437.0) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(8322.2,437.0) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(8313.8,437.2) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(8313.8,437.2) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.2,131.5,7.5)->Screen(8219.8,439.9) Visible:N
W2S DEBUG: Foot(174.2,131.5,7.5)->Screen(8219.8,439.9) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.029 -0.209 -0.209]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.5,131.1,7.5)->Screen(8135.2,442.8) Visible:N
W2S DEBUG: Foot(174.5,131.1,7.5)->Screen(8135.2,442.8) Visible:N
W2S DEBUG: Head(175.1,131.1,7.5)->Screen(8203.3,441.8) Visible:N
W2S DEBUG: Foot(175.1,131.1,7.5)->Screen(8203.3,441.8) Visible:N
W2S DEBUG: Head(175.7,131.1,7.5)->Screen(8287.3,440.6) Visible:N
W2S DEBUG: Foot(175.7,131.1,7.5)->Screen(8287.3,440.6) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(176.4,131.1,7.5)->Screen(8376.1,439.4) Visible:N
W2S DEBUG: Foot(176.4,131.1,7.5)->Screen(8376.1,439.4) Visible:N
W2S DEBUG: Head(177.1,131.1,7.5)->Screen(8467.3,438.1) Visible:N
W2S DEBUG: Foot(177.1,131.1,7.5)->Screen(8467.3,438.1) Visible:N
W2S DEBUG: Head(177.9,131.1,7.5)->Screen(8566.5,436.8) Visible:N
W2S DEBUG: Foot(177.9,131.1,7.5)->Screen(8566.5,436.8) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(178.7,131.1,7.5)->Screen(8671.7,435.3) Visible:N
W2S DEBUG: Foot(178.7,131.1,7.5)->Screen(8671.7,435.3) Visible:N
W2S DEBUG: Head(179.4,131.1,7.5)->Screen(8770.3,433.9) Visible:N
W2S DEBUG: Foot(179.4,131.1,7.5)->Screen(8770.3,433.9) Visible:N
W2S DEBUG: Head(180.1,131.1,7.5)->Screen(8872.9,432.5) Visible:N
W2S DEBUG: Foot(180.1,131.1,7.5)->Screen(8872.9,432.5) Visible:N
W2S DEBUG: Head(180.8,131.1,7.5)->Screen(8972.2,431.1) Visible:N
W2S DEBUG: Foot(180.8,131.1,7.5)->Screen(8972.2,431.1) Visible:N
W2S DEBUG: Head(181.5,131.1,7.5)->Screen(9058.5,430.0) Visible:N
W2S DEBUG: Foot(181.5,131.1,7.5)->Screen(9058.5,430.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(181.5,131.1,7.5)->Screen(9070.1,429.8) Visible:N
W2S DEBUG: Foot(181.5,131.1,7.5)->Screen(9070.1,429.8) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.029 -0.209 -0.209]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(181.1,131.1,7.5)->Screen(9014.1,430.6) Visible:N
W2S DEBUG: Foot(181.1,131.1,7.5)->Screen(9014.1,430.6) Visible:N
W2S DEBUG: Head(180.4,131.1,7.5)->Screen(8914.6,431.9) Visible:N
W2S DEBUG: Foot(180.4,131.1,7.5)->Screen(8914.6,431.9) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(179.5,131.1,7.5)->Screen(8785.7,433.8) Visible:N
W2S DEBUG: Foot(179.5,131.1,7.5)->Screen(8785.7,433.8) Visible:N
W2S DEBUG: Head(178.6,131.1,7.5)->Screen(8653.9,435.6) Visible:N
W2S DEBUG: Foot(178.6,131.1,7.5)->Screen(8653.9,435.6) Visible:N
W2S DEBUG: Head(177.5,131.2,7.5)->Screen(8560.0,436.4) Visible:N
W2S DEBUG: Foot(177.5,131.2,7.5)->Screen(8560.0,436.4) Visible:N
W2S DEBUG: Head(176.5,131.5,7.5)->Screen(8511.6,436.0) Visible:N
W2S DEBUG: Foot(176.5,131.5,7.5)->Screen(8511.6,436.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.6,131.9,7.5)->Screen(8379.4,436.2) Visible:N
W2S DEBUG: Foot(174.6,131.9,7.5)->Screen(8379.4,436.2) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(8313.0,437.3) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(8313.0,437.3) Visible:N
W2S DEBUG: Head(174.1,131.3,7.5)->Screen(8131.0,442.2) Visible:N
W2S DEBUG: Foot(174.1,131.3,7.5)->Screen(8131.0,442.2) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8083.9,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8083.9,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8083.9,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8083.9,443.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8083.9,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8083.9,443.5) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.978 -0.029 -0.209 -0.209]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.87, 176.81, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8084.1,443.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8084.1,443.5) Visible:N
W2S DEBUG: Head(174.1,131.3,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.3,7.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(117519.3,-2160.2) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(117519.3,-2160.2) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(5412.0,677.5) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(5412.0,677.5) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(5089.8,673.2) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(5089.8,673.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.98, 177.34, 4.50
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2017.6,589.6) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2017.6,589.6) Visible:Y
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(1339.7,582.3) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(1339.7,582.3) Visible:Y
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(1339.7,582.3) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(1339.7,582.3) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(1427.6,592.3) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(1427.6,592.3) Visible:Y
W2S DEBUG: Head(174.1,131.6,7.5)->Screen(1783.8,633.1) Visible:Y
W2S DEBUG: Foot(174.1,131.6,7.5)->Screen(1783.8,633.1) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1803.7,637.0) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1803.7,637.0) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.709 0.045 0.705 0.704]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.98, 177.34, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1798.9,639.1) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1798.9,639.1) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: Y, ForceAll: Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1760.0,658.0) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1760.0,658.0) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1746.7,658.3) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1746.7,658.3) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1739.8,666.8) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1739.8,666.8) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1733.6,666.9) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1733.6,666.9) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1733.2,667.0) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1733.2,667.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 64.22, 180.16, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1733.1,667.0) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1733.1,667.0) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1733.1,667.0) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1733.1,667.0) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1730.8,667.0) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1730.8,667.0) Visible:Y
W2S DEBUG: Head(174.1,131.3,7.5)->Screen(1733.1,666.9) Visible:Y
W2S DEBUG: Foot(174.1,131.3,7.5)->Screen(1733.1,666.9) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1682.3,669.0) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1682.3,669.0) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1152.2,659.8) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1152.2,659.8) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.308 0.036 0.952 0.951]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.37, 177.58, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1154.0,659.5) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1154.0,659.5) Visible:Y
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1154.6,659.5) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1154.6,659.5) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1158.0,659.9) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1158.0,659.9) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1276.9,660.3) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1276.9,660.3) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2137.3,658.7) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2137.3,658.7) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2190.9,686.2) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2190.9,686.2) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 62.78, 167.90, 7.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1568.8,692.7) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1568.8,692.7) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1465.5,688.7) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1465.5,688.7) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1104.7,684.6) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1104.7,684.6) Visible:Y
W2S DEBUG: Head(174.1,131.8,7.5)->Screen(1112.6,684.6) Visible:Y
W2S DEBUG: Foot(174.1,131.8,7.5)->Screen(1112.6,684.6) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: Y, ForceAll: Y
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(1113.1,684.6) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(1113.1,684.6) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(1050.8,690.7) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(1050.8,690.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.714 0.066 0.700 0.699]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 62.88, 167.35, 7.50
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(-1262.2,652.0) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(-1262.2,652.0) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(-7425.6,611.2) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(-7425.6,611.2) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(-7406.4,611.2) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(-7406.4,611.2) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(-6471.5,607.1) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(-6471.5,607.1) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 62.88, 167.35, 7.50
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(918.9,574.3) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(918.9,574.3) Visible:Y
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2055.9,574.3) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2055.9,574.3) Visible:Y
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2541.5,575.6) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2541.5,575.6) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.4,131.9,7.5)->Screen(3556.5,555.0) Visible:N
W2S DEBUG: Foot(174.4,131.9,7.5)->Screen(3556.5,555.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 -0.001 -0.006 -0.006]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 66.97, 173.29, 5.35
W2S DEBUG: Head(175.4,131.9,7.5)->Screen(4723.1,551.2) Visible:N
W2S DEBUG: Foot(175.4,131.9,7.5)->Screen(4723.1,551.2) Visible:N
W2S DEBUG: Head(176.8,131.9,7.5)->Screen(5040.2,517.4) Visible:N
W2S DEBUG: Foot(176.8,131.9,7.5)->Screen(5040.2,517.4) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(178.6,131.9,7.5)->Screen(7170.0,471.0) Visible:N
W2S DEBUG: Foot(178.6,131.9,7.5)->Screen(7170.0,471.0) Visible:N
W2S DEBUG: Head(180.5,131.7,7.5)->Screen(8887.2,429.1) Visible:N
W2S DEBUG: Foot(180.5,131.7,7.5)->Screen(8887.2,429.1) Visible:N
W2S DEBUG: Head(181.4,131.1,7.5)->Screen(11732.5,358.4) Visible:N
W2S DEBUG: Foot(181.4,131.1,7.5)->Screen(11732.5,358.4) Visible:N
W2S DEBUG: Head(181.5,131.1,7.5)->Screen(12027.4,351.3) Visible:N
W2S DEBUG: Foot(181.5,131.1,7.5)->Screen(12027.4,351.3) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.37, 173.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(180.8,131.1,7.5)->Screen(11872.5,353.9) Visible:N
W2S DEBUG: Foot(180.8,131.1,7.5)->Screen(11872.5,353.9) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(179.3,131.1,7.5)->Screen(11463.8,360.9) Visible:N
W2S DEBUG: Foot(179.3,131.1,7.5)->Screen(11463.8,360.9) Visible:N
W2S DEBUG: Head(177.3,131.1,7.5)->Screen(11117.1,365.6) Visible:N
W2S DEBUG: Foot(177.3,131.1,7.5)->Screen(11117.1,365.6) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(175.2,131.1,7.5)->Screen(13924.2,288.8) Visible:N
W2S DEBUG: Foot(175.2,131.1,7.5)->Screen(13924.2,288.8) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(13517.9,296.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(13517.9,296.6) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(13517.9,296.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(13517.9,296.6) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.964 -0.037 -0.264 -0.264]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(13517.9,296.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(13517.9,296.6) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(13517.9,296.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(13517.9,296.6) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(13523.9,296.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(13523.9,296.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(13558.6,295.5) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(13558.6,295.5) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(13518.8,296.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(13518.8,296.6) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.5,7.5)->Screen(13864.6,287.2) Visible:N
W2S DEBUG: Foot(174.1,131.5,7.5)->Screen(13864.6,287.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15745.9,237.5) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15745.9,237.5) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.962 -0.038 -0.273 -0.273]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15730.7,237.8) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15732.4,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15732.4,237.8) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15728.5,237.9) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15728.5,237.9) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15728.5,237.9) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15728.5,237.9) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15728.5,237.9) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15728.5,237.9) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15728.5,237.9) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15728.5,237.9) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15728.5,237.9) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15728.5,237.9) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15753.1,237.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15753.1,237.4) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.8,131.9,7.5)->Screen(16083.0,230.7) Visible:N
W2S DEBUG: Foot(174.8,131.9,7.5)->Screen(16083.0,230.7) Visible:N
W2S DEBUG: Head(176.0,131.9,7.5)->Screen(16727.6,217.8) Visible:N
W2S DEBUG: Foot(176.0,131.9,7.5)->Screen(16727.6,217.8) Visible:N
W2S DEBUG: Head(177.5,131.9,7.5)->Screen(17636.5,199.5) Visible:N
W2S DEBUG: Foot(177.5,131.9,7.5)->Screen(17636.5,199.5) Visible:N
W2S DEBUG: Head(179.1,131.9,7.5)->Screen(18665.7,178.7) Visible:N
W2S DEBUG: Foot(179.1,131.9,7.5)->Screen(18665.7,178.7) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.962 -0.038 -0.273 -0.273]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(180.7,131.9,7.5)->Screen(19883.9,154.2) Visible:N
W2S DEBUG: Foot(180.7,131.9,7.5)->Screen(19883.9,154.2) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(181.9,131.9,7.5)->Screen(20792.7,135.9) Visible:N
W2S DEBUG: Foot(181.9,131.9,7.5)->Screen(20792.7,135.9) Visible:N
W2S DEBUG: Head(181.9,131.9,7.5)->Screen(20792.7,135.9) Visible:N
W2S DEBUG: Foot(181.9,131.9,7.5)->Screen(20792.7,135.9) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(181.9,131.8,7.5)->Screen(20601.7,140.7) Visible:N
W2S DEBUG: Foot(181.9,131.8,7.5)->Screen(20601.7,140.7) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(19142.8,177.6) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(19142.8,177.6) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(19142.8,177.6) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(19142.8,177.6) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(19142.8,177.6) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(19142.8,177.6) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(19137.5,177.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(19137.5,177.7) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(181.4,131.1,7.5)->Screen(18814.5,184.1) Visible:N
W2S DEBUG: Foot(181.4,131.1,7.5)->Screen(18814.5,184.1) Visible:N
W2S DEBUG: Head(180.4,131.1,7.5)->Screen(18162.0,197.0) Visible:N
W2S DEBUG: Foot(180.4,131.1,7.5)->Screen(18162.0,197.0) Visible:N
W2S DEBUG: Head(178.9,131.1,7.5)->Screen(17220.9,215.6) Visible:N
W2S DEBUG: Foot(178.9,131.1,7.5)->Screen(17220.9,215.6) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(177.2,131.1,7.5)->Screen(16262.7,234.5) Visible:N
W2S DEBUG: Foot(177.2,131.1,7.5)->Screen(16262.7,234.5) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.962 -0.038 -0.273 -0.273]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(175.2,131.1,7.5)->Screen(15279.3,253.9) Visible:N
W2S DEBUG: Foot(175.2,131.1,7.5)->Screen(15279.3,253.9) Visible:N
W2S DEBUG: Head(174.2,131.2,7.5)->Screen(14843.6,262.0) Visible:N
W2S DEBUG: Foot(174.2,131.2,7.5)->Screen(14843.6,262.0) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(15731.8,237.8) Visible:N
W2S DEBUG: Head(174.1,131.5,7.5)->Screen(15238.3,251.2) Visible:N
W2S DEBUG: Foot(174.1,131.5,7.5)->Screen(15238.3,251.2) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(14773.3,263.8) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(14773.3,263.8) Visible:N
W2S DEBUG: Head(174.6,131.1,7.5)->Screen(15000.9,259.3) Visible:N
W2S DEBUG: Foot(174.6,131.1,7.5)->Screen(15000.9,259.3) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(175.3,131.1,7.5)->Screen(15317.6,253.0) Visible:N
W2S DEBUG: Foot(175.3,131.1,7.5)->Screen(15317.6,253.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.962 -0.038 -0.273 -0.273]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(175.0,131.1,7.5)->Screen(15176.4,255.8) Visible:N
W2S DEBUG: Foot(175.0,131.1,7.5)->Screen(15176.4,255.8) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2652.5,604.9) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2652.5,604.9) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2911.4,614.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2911.4,614.0) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2911.4,614.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2911.4,614.0) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2921.0,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2921.0,624.0) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.3,624.0) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.9,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.9,624.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.959 0.016 0.285 0.285]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.8,7.5)->Screen(2939.6,623.5) Visible:N
W2S DEBUG: Foot(174.1,131.8,7.5)->Screen(2939.6,623.5) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.5,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.5,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.5,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.5,623.4) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.8,7.5)->Screen(2940.2,623.5) Visible:N
W2S DEBUG: Foot(174.1,131.8,7.5)->Screen(2940.2,623.5) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.8,7.5)->Screen(2938.9,623.5) Visible:N
W2S DEBUG: Foot(174.1,131.8,7.5)->Screen(2938.9,623.5) Visible:N
W2S DEBUG: Head(174.1,131.8,7.5)->Screen(2938.3,623.5) Visible:N
W2S DEBUG: Foot(174.1,131.8,7.5)->Screen(2938.3,623.5) Visible:N
W2S DEBUG: Head(174.1,131.7,7.5)->Screen(2938.0,623.6) Visible:N
W2S DEBUG: Foot(174.1,131.7,7.5)->Screen(2938.0,623.6) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.0,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.0,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.0,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.0,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.0,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.0,623.4) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.959 0.016 0.285 0.285]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2941.9,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.4,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.7,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.7,623.4) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.959 0.016 0.285 0.285]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.7,131.9,7.5)->Screen(2949.2,623.6) Visible:N
W2S DEBUG: Foot(174.7,131.9,7.5)->Screen(2949.2,623.6) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(175.9,131.9,7.5)->Screen(2961.8,623.8) Visible:N
W2S DEBUG: Foot(175.9,131.9,7.5)->Screen(2961.8,623.8) Visible:N
W2S DEBUG: Head(177.5,131.9,7.5)->Screen(2978.2,624.2) Visible:N
W2S DEBUG: Foot(177.5,131.9,7.5)->Screen(2978.2,624.2) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(179.0,131.9,7.5)->Screen(2994.2,624.5) Visible:N
W2S DEBUG: Foot(179.0,131.9,7.5)->Screen(2994.2,624.5) Visible:N
W2S DEBUG: Head(180.5,131.9,7.5)->Screen(3010.0,624.8) Visible:N
W2S DEBUG: Foot(180.5,131.9,7.5)->Screen(3010.0,624.8) Visible:N
W2S DEBUG: Head(181.9,131.9,7.5)->Screen(3023.9,625.1) Visible:N
W2S DEBUG: Foot(181.9,131.9,7.5)->Screen(3023.9,625.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(181.9,131.9,7.5)->Screen(3024.0,625.1) Visible:N
W2S DEBUG: Foot(181.9,131.9,7.5)->Screen(3024.0,625.1) Visible:N
W2S DEBUG: Head(181.9,131.9,7.5)->Screen(3024.0,625.1) Visible:N
W2S DEBUG: Foot(181.9,131.9,7.5)->Screen(3024.0,625.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(181.9,131.9,7.5)->Screen(3024.0,625.1) Visible:N
W2S DEBUG: Foot(181.9,131.9,7.5)->Screen(3024.0,625.1) Visible:N
W2S DEBUG: Head(181.9,131.6,7.5)->Screen(3016.0,625.3) Visible:N
W2S DEBUG: Foot(181.9,131.6,7.5)->Screen(3016.0,625.3) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3002.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3002.4,625.7) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3002.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3002.4,625.7) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.959 0.016 0.285 0.285]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3002.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3002.4,625.7) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3002.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3002.4,625.7) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3002.5,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3002.5,625.7) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(181.7,131.1,7.5)->Screen(3000.8,625.6) Visible:N
W2S DEBUG: Foot(181.7,131.1,7.5)->Screen(3000.8,625.6) Visible:N
W2S DEBUG: Head(181.2,131.1,7.5)->Screen(2995.2,625.5) Visible:N
W2S DEBUG: Foot(181.2,131.1,7.5)->Screen(2995.2,625.5) Visible:N
W2S DEBUG: Head(180.0,131.1,7.5)->Screen(2983.0,625.3) Visible:N
W2S DEBUG: Foot(180.0,131.1,7.5)->Screen(2983.0,625.3) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(178.2,131.1,7.5)->Screen(2964.0,624.9) Visible:N
W2S DEBUG: Foot(178.2,131.1,7.5)->Screen(2964.0,624.9) Visible:N
W2S DEBUG: Head(176.2,131.4,7.5)->Screen(2950.5,624.3) Visible:N
W2S DEBUG: Foot(176.2,131.4,7.5)->Screen(2950.5,624.3) Visible:N
W2S DEBUG: Head(174.4,131.9,7.5)->Screen(2945.3,623.5) Visible:N
W2S DEBUG: Foot(174.4,131.9,7.5)->Screen(2945.3,623.5) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.6,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.6,623.4) Visible:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.6,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.6,623.4) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.8,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.8,623.4) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.959 0.016 0.285 0.285]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.6,7.5)->Screen(2935.4,623.6) Visible:N
W2S DEBUG: Foot(174.1,131.6,7.5)->Screen(2935.4,623.6) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2921.1,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2921.1,624.0) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.6,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.6,624.0) Visible:N
W2S DEBUG: Head(174.3,131.1,7.5)->Screen(2922.8,624.1) Visible:N
W2S DEBUG: Foot(174.3,131.1,7.5)->Screen(2922.8,624.1) Visible:N
W2S DEBUG: Head(174.8,131.1,7.5)->Screen(2928.1,624.2) Visible:N
W2S DEBUG: Foot(174.8,131.1,7.5)->Screen(2928.1,624.2) Visible:N
W2S DEBUG: Head(174.3,131.1,7.5)->Screen(2923.2,624.1) Visible:N
W2S DEBUG: Foot(174.3,131.1,7.5)->Screen(2923.2,624.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.8,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.8,624.0) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.8,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.8,624.0) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.8,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.8,624.0) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:100 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.8,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.8,624.0) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.4,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.4,624.0) Visible:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: Y, ForceAll: Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.4,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.4,624.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.959 0.016 0.285 0.285]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(2920.2,624.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(2920.2,624.0) Visible:N
W2S DEBUG: Head(174.9,131.1,7.5)->Screen(2928.9,624.2) Visible:N
W2S DEBUG: Foot(174.9,131.1,7.5)->Screen(2928.9,624.2) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(176.5,131.1,7.5)->Screen(2946.1,624.6) Visible:N
W2S DEBUG: Foot(176.5,131.1,7.5)->Screen(2946.1,624.6) Visible:N
W2S DEBUG: Head(178.6,131.1,7.5)->Screen(2968.3,625.0) Visible:N
W2S DEBUG: Foot(178.6,131.1,7.5)->Screen(2968.3,625.0) Visible:N
W2S DEBUG: Head(180.8,131.1,7.5)->Screen(2991.0,625.5) Visible:N
W2S DEBUG: Foot(180.8,131.1,7.5)->Screen(2991.0,625.5) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:56 T:0 A:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: Y, ForceAll: Y
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:56 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.959 0.016 0.285 0.285]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: Y, ForceAll: Y
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.4,625.7) Visible:N
W2S DEBUG: Head(181.9,131.1,7.5)->Screen(3001.6,625.7) Visible:N
W2S DEBUG: Foot(181.9,131.1,7.5)->Screen(3001.6,625.7) Visible:N
W2S DEBUG: Head(181.2,131.1,7.5)->Screen(2995.0,625.5) Visible:N
W2S DEBUG: Foot(181.2,131.1,7.5)->Screen(2995.0,625.5) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(179.5,131.1,7.5)->Screen(2977.3,625.2) Visible:N
W2S DEBUG: Foot(179.5,131.1,7.5)->Screen(2977.3,625.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.35, 173.10, 4.50
W2S DEBUG: Head(177.5,131.3,7.5)->Screen(2961.7,624.6) Visible:N
W2S DEBUG: Foot(177.5,131.3,7.5)->Screen(2961.7,624.6) Visible:N
W2S DEBUG: Head(175.4,131.8,7.5)->Screen(2952.4,623.8) Visible:N
W2S DEBUG: Foot(175.4,131.8,7.5)->Screen(2952.4,623.8) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:56 T:0 A:N
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(2942.6,623.4) Visible:N
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(2942.6,623.4) Visible:N
W2S DEBUG: Head(174.1,131.8,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.8,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(174.1,131.3,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.3,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.499 -0.161 -0.862 -0.862]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.20, 173.61, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(0.0,0.0) Visible:N
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(3586.8,628.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(3586.8,628.6) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:56 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(3586.8,628.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(3586.8,628.6) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(4233.4,582.8) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(4233.4,582.8) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8352.8,481.3) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8352.8,481.3) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8352.8,481.3) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8352.8,481.3) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(8352.8,481.3) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(8352.8,481.3) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(12466.9,370.9) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(12466.9,370.9) Visible:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(23771.4,76.9) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(23771.4,76.9) Visible:N
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(30490.4,-97.6) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(30490.4,-97.6) Visible:N
ENTITY DEBUG [4]: 0x0FDB0C88 - H:56 T:0 A:N
W2S DEBUG: Head(174.2,131.1,7.5)->Screen(84141.6,-1482.6) Visible:N
W2S DEBUG: Foot(174.2,131.1,7.5)->Screen(84141.6,-1482.6) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.935 -0.021 -0.354 -0.354]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(174.7,131.1,7.5)->Screen(93426.7,-1715.1) Visible:N
W2S DEBUG: Foot(174.7,131.1,7.5)->Screen(93426.7,-1715.1) Visible:N
W2S DEBUG: Head(175.2,131.1,7.5)->Screen(105751.4,-2023.8) Visible:N
W2S DEBUG: Foot(175.2,131.1,7.5)->Screen(105751.4,-2023.8) Visible:N
W2S DEBUG: Head(174.7,131.1,7.5)->Screen(93204.8,-1709.6) Visible:N
W2S DEBUG: Foot(174.7,131.1,7.5)->Screen(93204.8,-1709.6) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(4973.3,601.6) Visible:N
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(4973.3,601.6) Visible:N
W2S DEBUG: Head(174.1,131.3,7.5)->Screen(1935.2,617.0) Visible:Y
W2S DEBUG: Foot(174.1,131.3,7.5)->Screen(1935.2,617.0) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: Y, ForceAll: Y
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(1566.3,634.6) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(1566.3,634.6) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:56 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(174.1,131.9,7.5)->Screen(1498.7,634.9) Visible:Y
W2S DEBUG: Foot(174.1,131.9,7.5)->Screen(1498.7,634.9) Visible:Y
W2S DEBUG: Head(174.1,131.6,7.5)->Screen(1435.9,635.2) Visible:Y
W2S DEBUG: Foot(174.1,131.6,7.5)->Screen(1435.9,635.2) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1430.5,635.2) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1430.5,635.2) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1430.3,635.2) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1430.3,635.2) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1430.4,635.2) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1430.4,635.2) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.5,131.1,7.5)->Screen(1431.9,635.3) Visible:Y
W2S DEBUG: Foot(174.5,131.1,7.5)->Screen(1431.9,635.3) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.475 0.062 0.880 0.879]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1430.5,635.2) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1430.5,635.2) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1430.5,635.2) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1430.5,635.2) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(174.1,131.1,7.5)->Screen(1430.5,635.2) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.5)->Screen(1430.5,635.2) Visible:Y
W2S DEBUG: Head(174.1,131.1,7.8)->Screen(1430.1,632.0) Visible:Y
W2S DEBUG: Foot(174.1,131.1,7.8)->Screen(1430.1,632.0) Visible:Y
W2S DEBUG: Head(168.6,127.4,11.9)->Screen(1366.9,583.9) Visible:Y
W2S DEBUG: Foot(168.6,127.4,11.9)->Screen(1366.9,583.9) Visible:Y
W2S DEBUG: Head(163.9,123.7,13.8)->Screen(1303.3,558.6) Visible:Y
W2S DEBUG: Foot(163.9,123.7,13.8)->Screen(1303.3,558.6) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(161.9,122.0,14.3)->Screen(1274.9,552.4) Visible:Y
W2S DEBUG: Foot(161.9,122.0,14.3)->Screen(1274.9,552.4) Visible:Y
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(160.0,120.3,14.4)->Screen(1245.1,550.1) Visible:Y
W2S DEBUG: Foot(160.0,120.3,14.4)->Screen(1245.1,550.1) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: Y, ForceAll: Y
W2S DEBUG: Head(158.6,118.8,14.2)->Screen(1221.8,552.1) Visible:Y
W2S DEBUG: Foot(158.6,118.8,14.2)->Screen(1221.8,552.1) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(157.6,117.3,13.6)->Screen(1200.0,559.1) Visible:Y
W2S DEBUG: Foot(157.6,117.3,13.6)->Screen(1200.0,559.1) Visible:Y
W2S DEBUG: Head(156.7,116.1,12.8)->Screen(1181.6,568.4) Visible:Y
W2S DEBUG: Foot(156.7,116.1,12.8)->Screen(1181.6,568.4) Visible:Y
W2S DEBUG: Head(155.9,114.8,11.7)->Screen(1164.1,582.0) Visible:Y
W2S DEBUG: Foot(155.9,114.8,11.7)->Screen(1164.1,582.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.478 0.060 0.878 0.878]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(155.4,113.7,10.6)->Screen(1153.1,597.1) Visible:Y
W2S DEBUG: Foot(155.4,113.7,10.6)->Screen(1153.1,597.1) Visible:Y
W2S DEBUG: Head(155.1,112.7,11.9)->Screen(1140.4,581.1) Visible:Y
W2S DEBUG: Foot(155.1,112.7,11.9)->Screen(1140.4,581.1) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(154.9,111.7,12.9)->Screen(1129.6,570.1) Visible:Y
W2S DEBUG: Foot(154.9,111.7,12.9)->Screen(1129.6,570.1) Visible:Y
W2S DEBUG: Head(154.8,110.8,13.4)->Screen(1120.4,563.6) Visible:Y
W2S DEBUG: Foot(154.8,110.8,13.4)->Screen(1120.4,563.6) Visible:Y
W2S DEBUG: Head(154.9,110.0,13.6)->Screen(1113.0,561.5) Visible:Y
W2S DEBUG: Foot(154.9,110.0,13.6)->Screen(1113.0,561.5) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(155.1,109.3,13.5)->Screen(1107.2,563.3) Visible:Y
W2S DEBUG: Foot(155.1,109.3,13.5)->Screen(1107.2,563.3) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(155.4,108.7,13.2)->Screen(1103.4,568.3) Visible:Y
W2S DEBUG: Foot(155.4,108.7,13.2)->Screen(1103.4,568.3) Visible:Y
W2S DEBUG: Head(155.8,108.1,12.5)->Screen(1100.9,576.9) Visible:Y
W2S DEBUG: Foot(155.8,108.1,12.5)->Screen(1100.9,576.9) Visible:Y
W2S DEBUG: Head(156.2,107.7,11.5)->Screen(1100.1,588.8) Visible:Y
W2S DEBUG: Foot(156.2,107.7,11.5)->Screen(1100.1,588.8) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(156.7,107.4,10.3)->Screen(1100.7,603.6) Visible:Y
W2S DEBUG: Foot(156.7,107.4,10.3)->Screen(1100.7,603.6) Visible:Y
W2S DEBUG: Head(157.2,107.2,8.8)->Screen(1102.8,621.2) Visible:Y
W2S DEBUG: Foot(157.2,107.2,8.8)->Screen(1102.8,621.2) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: Y, ForceAll: Y
W2S DEBUG: Head(157.8,107.2,7.5)->Screen(1106.1,636.4) Visible:Y
W2S DEBUG: Foot(157.8,107.2,7.5)->Screen(1106.1,636.4) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.478 0.060 0.878 0.878]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(158.3,107.5,7.5)->Screen(1112.8,636.5) Visible:Y
W2S DEBUG: Foot(158.3,107.5,7.5)->Screen(1112.8,636.5) Visible:Y
W2S DEBUG: Head(158.7,108.2,7.5)->Screen(1121.9,636.5) Visible:Y
W2S DEBUG: Foot(158.7,108.2,7.5)->Screen(1121.9,636.5) Visible:Y
W2S DEBUG: Head(159.1,109.0,7.5)->Screen(1132.3,636.4) Visible:Y
W2S DEBUG: Foot(159.1,109.0,7.5)->Screen(1132.3,636.4) Visible:Y
W2S DEBUG: Head(159.5,109.9,7.5)->Screen(1143.0,636.4) Visible:Y
W2S DEBUG: Foot(159.5,109.9,7.5)->Screen(1143.0,636.4) Visible:Y
W2S DEBUG: Head(160.1,110.7,8.5)->Screen(1154.7,625.5) Visible:Y
W2S DEBUG: Foot(160.1,110.7,8.5)->Screen(1154.7,625.5) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(160.9,110.9,8.5)->Screen(1161.9,625.2) Visible:Y
W2S DEBUG: Foot(160.9,110.9,8.5)->Screen(1161.9,625.2) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(161.8,110.9,8.5)->Screen(1167.8,625.6) Visible:Y
W2S DEBUG: Foot(161.8,110.9,8.5)->Screen(1167.8,625.6) Visible:Y
W2S DEBUG: Head(162.9,110.9,8.5)->Screen(1174.5,625.9) Visible:Y
W2S DEBUG: Foot(162.9,110.9,8.5)->Screen(1174.5,625.9) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(163.9,110.9,8.5)->Screen(1181.0,626.3) Visible:Y
W2S DEBUG: Foot(163.9,110.9,8.5)->Screen(1181.0,626.3) Visible:Y
W2S DEBUG: Head(164.9,110.9,8.5)->Screen(1187.0,626.6) Visible:Y
W2S DEBUG: Foot(164.9,110.9,8.5)->Screen(1187.0,626.6) Visible:Y
W2S DEBUG: Head(166.0,110.9,8.5)->Screen(1193.4,627.0) Visible:Y
W2S DEBUG: Foot(166.0,110.9,8.5)->Screen(1193.4,627.0) Visible:Y
W2S DEBUG: Head(167.1,110.9,8.5)->Screen(1200.1,627.4) Visible:Y
W2S DEBUG: Foot(167.1,110.9,8.5)->Screen(1200.1,627.4) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.478 0.060 0.878 0.878]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(168.2,111.0,8.4)->Screen(1207.8,628.8) Visible:Y
W2S DEBUG: Foot(168.2,111.0,8.4)->Screen(1207.8,628.8) Visible:Y
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(169.3,111.2,7.6)->Screen(1215.7,637.4) Visible:Y
W2S DEBUG: Foot(169.3,111.2,7.6)->Screen(1215.7,637.4) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: Y, ForceAll: Y
W2S DEBUG: Head(170.5,111.4,8.0)->Screen(1224.1,633.2) Visible:Y
W2S DEBUG: Foot(170.5,111.4,8.0)->Screen(1224.1,633.2) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(171.5,111.6,8.9)->Screen(1231.3,624.2) Visible:Y
W2S DEBUG: Foot(171.5,111.6,8.9)->Screen(1231.3,624.2) Visible:Y
W2S DEBUG: Head(172.6,111.8,9.2)->Screen(1239.1,621.7) Visible:Y
W2S DEBUG: Foot(172.6,111.8,9.2)->Screen(1239.1,621.7) Visible:Y
W2S DEBUG: Head(173.7,112.1,9.2)->Screen(1246.9,622.0) Visible:Y
W2S DEBUG: Foot(173.7,112.1,9.2)->Screen(1246.9,622.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(174.8,112.3,8.6)->Screen(1254.5,629.0) Visible:Y
W2S DEBUG: Foot(174.8,112.3,8.6)->Screen(1254.5,629.0) Visible:Y
W2S DEBUG: Head(175.9,112.4,8.5)->Screen(1261.2,629.8) Visible:Y
W2S DEBUG: Foot(175.9,112.4,8.5)->Screen(1261.2,629.8) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(176.9,112.4,8.5)->Screen(1266.5,630.1) Visible:Y
W2S DEBUG: Foot(176.9,112.4,8.5)->Screen(1266.5,630.1) Visible:Y
W2S DEBUG: Head(178.0,112.4,8.5)->Screen(1271.7,630.4) Visible:Y
W2S DEBUG: Foot(178.0,112.4,8.5)->Screen(1271.7,630.4) Visible:Y
W2S DEBUG: Head(179.1,112.3,8.5)->Screen(1276.4,630.7) Visible:Y
W2S DEBUG: Foot(179.1,112.3,8.5)->Screen(1276.4,630.7) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(180.3,112.2,8.5)->Screen(1280.9,631.1) Visible:Y
W2S DEBUG: Foot(180.3,112.2,8.5)->Screen(1280.9,631.1) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.478 0.060 0.878 0.878]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(181.3,112.1,8.7)->Screen(1284.8,629.5) Visible:Y
W2S DEBUG: Foot(181.3,112.1,8.7)->Screen(1284.8,629.5) Visible:Y
W2S DEBUG: Head(182.4,112.1,8.8)->Screen(1290.3,629.0) Visible:Y
W2S DEBUG: Foot(182.4,112.1,8.8)->Screen(1290.3,629.0) Visible:Y
W2S DEBUG: Head(183.4,112.4,8.5)->Screen(1297.0,631.9) Visible:Y
W2S DEBUG: Foot(183.4,112.4,8.5)->Screen(1297.0,631.9) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(184.2,112.9,8.5)->Screen(1305.5,632.0) Visible:Y
W2S DEBUG: Foot(184.2,112.9,8.5)->Screen(1305.5,632.0) Visible:Y
W2S DEBUG: Head(184.8,113.7,7.9)->Screen(1314.6,638.0) Visible:Y
W2S DEBUG: Foot(184.8,113.7,7.9)->Screen(1314.6,638.0) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
W2S DEBUG: Head(185.3,114.5,7.5)->Screen(1323.8,641.8) Visible:Y
W2S DEBUG: Foot(185.3,114.5,7.5)->Screen(1323.8,641.8) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(185.4,115.4,7.5)->Screen(1332.3,641.7) Visible:Y
W2S DEBUG: Foot(185.4,115.4,7.5)->Screen(1332.3,641.7) Visible:Y
W2S DEBUG: Head(185.3,116.3,7.5)->Screen(1339.4,641.6) Visible:Y
W2S DEBUG: Foot(185.3,116.3,7.5)->Screen(1339.4,641.6) Visible:Y
W2S DEBUG: Head(184.9,117.3,7.5)->Screen(1346.2,641.4) Visible:Y
W2S DEBUG: Foot(184.9,117.3,7.5)->Screen(1346.2,641.4) Visible:Y
W2S DEBUG: Head(184.3,118.1,7.5)->Screen(1350.9,641.2) Visible:Y
W2S DEBUG: Foot(184.3,118.1,7.5)->Screen(1350.9,641.2) Visible:Y
W2S DEBUG: Head(183.6,118.8,7.5)->Screen(1354.5,641.0) Visible:Y
W2S DEBUG: Foot(183.6,118.8,7.5)->Screen(1354.5,641.0) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(182.7,119.5,7.5)->Screen(1356.7,640.7) Visible:Y
W2S DEBUG: Foot(182.7,119.5,7.5)->Screen(1356.7,640.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.478 0.060 0.878 0.878]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(181.8,120.0,7.5)->Screen(1357.5,640.5) Visible:Y
W2S DEBUG: Foot(181.8,120.0,7.5)->Screen(1357.5,640.5) Visible:Y
W2S DEBUG: Head(180.9,120.5,7.5)->Screen(1357.8,640.2) Visible:Y
W2S DEBUG: Foot(180.9,120.5,7.5)->Screen(1357.8,640.2) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(179.9,121.1,7.5)->Screen(1359.9,639.9) Visible:Y
W2S DEBUG: Foot(179.9,121.1,7.5)->Screen(1359.9,639.9) Visible:Y
W2S DEBUG: Head(179.2,121.9,7.5)->Screen(1364.0,639.7) Visible:Y
W2S DEBUG: Foot(179.2,121.9,7.5)->Screen(1364.0,639.7) Visible:Y
W2S DEBUG: Head(178.7,122.8,7.5)->Screen(1370.5,639.5) Visible:Y
W2S DEBUG: Foot(178.7,122.8,7.5)->Screen(1370.5,639.5) Visible:Y
W2S DEBUG: Head(178.4,123.8,7.5)->Screen(1378.9,639.3) Visible:Y
W2S DEBUG: Foot(178.4,123.8,7.5)->Screen(1378.9,639.3) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(178.3,124.8,7.5)->Screen(1388.7,639.1) Visible:Y
W2S DEBUG: Foot(178.3,124.8,7.5)->Screen(1388.7,639.1) Visible:Y
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(178.4,125.9,7.5)->Screen(1399.4,639.0) Visible:Y
W2S DEBUG: Foot(178.4,125.9,7.5)->Screen(1399.4,639.0) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
W2S DEBUG: Head(178.5,127.0,7.6)->Screen(1410.7,638.0) Visible:Y
W2S DEBUG: Foot(178.5,127.0,7.6)->Screen(1410.7,638.0) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(178.8,128.1,8.5)->Screen(1422.4,628.0) Visible:Y
W2S DEBUG: Foot(178.8,128.1,8.5)->Screen(1422.4,628.0) Visible:Y
W2S DEBUG: Head(179.2,129.1,9.1)->Screen(1434.3,622.0) Visible:Y
W2S DEBUG: Foot(179.2,129.1,9.1)->Screen(1434.3,622.0) Visible:Y
W2S DEBUG: Head(179.5,130.1,9.1)->Screen(1445.4,621.9) Visible:Y
W2S DEBUG: Foot(179.5,130.1,9.1)->Screen(1445.4,621.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.478 0.060 0.878 0.878]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(180.0,131.1,9.1)->Screen(1457.4,622.0) Visible:Y
W2S DEBUG: Foot(180.0,131.1,9.1)->Screen(1457.4,622.0) Visible:Y
W2S DEBUG: Head(180.7,132.5,9.1)->Screen(1474.5,621.4) Visible:Y
W2S DEBUG: Foot(180.7,132.5,9.1)->Screen(1474.5,621.4) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(181.9,134.2,9.1)->Screen(1497.0,621.3) Visible:Y
W2S DEBUG: Foot(181.9,134.2,9.1)->Screen(1496.7,621.3) Visible:Y
W2S DEBUG: Head(183.0,134.0,9.0)->Screen(1498.7,623.1) Visible:Y
W2S DEBUG: Foot(183.0,134.0,9.0)->Screen(1498.7,623.1) Visible:Y
W2S DEBUG: Head(183.0,132.5,9.0)->Screen(1483.1,623.3) Visible:Y
W2S DEBUG: Foot(183.0,132.5,9.0)->Screen(1483.1,623.3) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(182.4,130.7,9.1)->Screen(1462.3,622.2) Visible:Y
W2S DEBUG: Foot(182.4,130.7,9.1)->Screen(1462.3,622.2) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 67.19, 173.63, 4.50
W2S DEBUG: Head(181.3,128.8,9.1)->Screen(1439.6,622.8) Visible:Y
W2S DEBUG: Foot(181.3,128.8,9.1)->Screen(1439.6,622.8) Visible:Y
W2S DEBUG: Head(179.7,127.7,8.7)->Screen(1422.3,626.4) Visible:Y
W2S DEBUG: Foot(179.7,127.7,8.7)->Screen(1422.3,626.4) Visible:Y
W2S DEBUG: Head(177.7,127.3,8.5)->Screen(1248.9,672.3) Visible:Y
W2S DEBUG: Foot(177.7,127.3,8.5)->Screen(1248.9,672.3) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(175.6,127.7,8.5)->Screen(1178.9,645.1) Visible:Y
W2S DEBUG: Foot(175.6,127.7,8.5)->Screen(1178.9,645.1) Visible:Y
W2S DEBUG: Head(173.5,128.3,9.2)->Screen(589.8,612.7) Visible:Y
W2S DEBUG: Foot(173.5,128.3,9.2)->Screen(589.8,612.7) Visible:Y
W2S DEBUG: Head(171.4,128.7,9.2)->Screen(486.9,629.1) Visible:Y
W2S DEBUG: Foot(171.4,128.7,9.2)->Screen(486.9,629.1) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.147 -0.019 0.989 0.989]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 65.96, 173.78, 4.50
W2S DEBUG: Head(170.1,127.8,8.5)->Screen(471.8,680.6) Visible:Y
W2S DEBUG: Foot(170.1,127.8,8.5)->Screen(471.8,680.6) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
W2S DEBUG: Head(170.4,126.6,9.9)->Screen(456.1,679.8) Visible:Y
W2S DEBUG: Foot(170.4,126.6,9.9)->Screen(456.1,679.8) Visible:Y
W2S DEBUG: Head(171.4,125.9,11.4)->Screen(452.8,685.2) Visible:Y
W2S DEBUG: Foot(171.4,125.9,11.4)->Screen(452.8,685.2) Visible:Y
W2S DEBUG: Head(172.5,125.7,11.7)->Screen(462.6,716.5) Visible:Y
W2S DEBUG: Foot(172.5,125.7,11.7)->Screen(462.6,716.5) Visible:Y
W2S DEBUG: Head(174.0,125.9,10.7)->Screen(469.5,644.9) Visible:Y
W2S DEBUG: Foot(174.0,125.9,10.7)->Screen(469.5,644.9) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(175.6,126.4,8.6)->Screen(479.9,607.7) Visible:Y
W2S DEBUG: Foot(175.6,126.4,8.6)->Screen(479.9,607.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 63.30, 173.37, 4.50
W2S DEBUG: Head(177.8,127.9,10.0)->Screen(510.4,556.2) Visible:Y
W2S DEBUG: Foot(177.8,127.9,10.0)->Screen(510.4,556.2) Visible:Y
W2S DEBUG: Head(180.6,130.1,12.6)->Screen(554.4,520.4) Visible:Y
W2S DEBUG: Foot(180.6,130.1,12.6)->Screen(554.4,520.4) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(183.2,132.0,13.7)->Screen(596.3,546.3) Visible:Y
W2S DEBUG: Foot(183.2,132.0,13.7)->Screen(596.3,546.3) Visible:Y
W2S DEBUG: Head(184.9,133.2,13.3)->Screen(620.9,591.8) Visible:Y
W2S DEBUG: Foot(184.9,133.2,13.3)->Screen(620.9,591.8) Visible:Y
W2S DEBUG: Head(186.0,133.6,11.7)->Screen(633.9,627.6) Visible:Y
W2S DEBUG: Foot(186.0,133.6,11.7)->Screen(633.9,627.6) Visible:Y
W2S DEBUG: Head(186.6,133.5,9.0)->Screen(630.5,592.6) Visible:Y
W2S DEBUG: Foot(186.6,133.5,9.0)->Screen(630.5,592.6) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.161 0.201 0.981 0.980]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 61.10, 172.77, 5.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(187.2,132.8,8.8)->Screen(625.2,535.4) Visible:Y
W2S DEBUG: Foot(187.2,132.8,8.8)->Screen(625.2,535.4) Visible:Y
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(187.5,131.9,8.9)->Screen(612.8,519.5) Visible:Y
W2S DEBUG: Foot(187.5,131.9,8.9)->Screen(612.8,519.5) Visible:Y
W2S DEBUG: Head(187.2,130.9,7.5)->Screen(584.1,506.8) Visible:Y
W2S DEBUG: Foot(187.2,130.9,7.5)->Screen(584.1,506.8) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(185.5,131.1,8.1)->Screen(538.7,400.7) Visible:Y
W2S DEBUG: Foot(185.5,131.1,8.1)->Screen(538.7,400.7) Visible:Y
W2S DEBUG: Head(183.3,131.3,9.0)->Screen(508.0,331.8) Visible:Y
W2S DEBUG: Foot(183.3,131.3,9.0)->Screen(508.0,331.8) Visible:Y
W2S DEBUG: Head(181.2,131.1,8.6)->Screen(497.4,338.7) Visible:Y
W2S DEBUG: Foot(181.2,131.1,8.6)->Screen(497.4,338.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 61.10, 172.51, 5.50
W2S DEBUG: Head(178.6,129.9,9.0)->Screen(550.6,437.1) Visible:Y
W2S DEBUG: Foot(178.6,129.9,9.0)->Screen(550.6,437.1) Visible:Y
W2S DEBUG: Head(179.4,127.6,8.7)->Screen(1184.8,587.9) Visible:Y
W2S DEBUG: Foot(179.4,127.6,8.7)->Screen(1184.8,587.9) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(182.2,126.4,7.5)->Screen(1253.2,602.6) Visible:Y
W2S DEBUG: Foot(182.2,126.4,7.5)->Screen(1253.2,602.6) Visible:Y
W2S DEBUG: Head(184.3,126.3,7.5)->Screen(1259.3,602.9) Visible:Y
W2S DEBUG: Foot(184.3,126.3,7.5)->Screen(1259.3,602.9) Visible:Y
W2S DEBUG: Head(185.9,126.5,7.5)->Screen(1266.1,603.1) Visible:Y
W2S DEBUG: Foot(185.9,126.5,7.5)->Screen(1266.1,603.1) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(185.9,126.7,7.5)->Screen(1268.1,603.1) Visible:Y
W2S DEBUG: Foot(185.9,126.7,7.5)->Screen(1268.1,603.1) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.417 0.115 0.907 0.907]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 61.10, 172.51, 5.50
W2S DEBUG: Head(185.9,126.9,9.3)->Screen(1382.8,591.5) Visible:Y
W2S DEBUG: Foot(185.9,126.9,9.3)->Screen(1382.8,591.5) Visible:Y
W2S DEBUG: Head(185.9,126.8,10.6)->Screen(2184.7,643.5) Visible:Y
W2S DEBUG: Foot(185.9,126.8,10.6)->Screen(2184.7,643.5) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
W2S DEBUG: Head(185.9,126.1,10.6)->Screen(2221.7,649.5) Visible:Y
W2S DEBUG: Foot(185.9,126.1,10.6)->Screen(2221.7,649.5) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(186.9,125.1,8.9)->Screen(2212.5,669.6) Visible:Y
W2S DEBUG: Foot(186.9,125.1,8.9)->Screen(2212.5,669.6) Visible:Y
W2S DEBUG: Head(188.0,123.8,8.3)->Screen(2200.6,677.8) Visible:Y
W2S DEBUG: Foot(188.0,123.8,8.3)->Screen(2200.6,677.8) Visible:Y
W2S DEBUG: Head(188.5,122.4,8.1)->Screen(2184.6,680.5) Visible:Y
W2S DEBUG: Foot(188.5,122.4,8.1)->Screen(2184.6,680.5) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 61.10, 172.51, 5.50
W2S DEBUG: Head(187.8,122.1,7.5)->Screen(2177.8,687.1) Visible:Y
W2S DEBUG: Foot(187.8,122.1,7.5)->Screen(2177.8,687.1) Visible:Y
W2S DEBUG: Head(185.6,122.1,7.5)->Screen(2166.1,686.9) Visible:Y
W2S DEBUG: Foot(185.6,122.1,7.5)->Screen(2166.1,686.9) Visible:Y
W2S DEBUG: Head(183.1,122.5,7.5)->Screen(2157.3,686.5) Visible:Y
W2S DEBUG: Foot(183.1,122.5,7.5)->Screen(2157.3,686.5) Visible:Y
W2S DEBUG: Head(180.7,122.5,7.5)->Screen(2144.4,686.2) Visible:Y
W2S DEBUG: Foot(180.7,122.5,7.5)->Screen(2144.4,686.2) Visible:Y
W2S DEBUG: Head(178.5,122.1,7.5)->Screen(2127.3,686.0) Visible:Y
W2S DEBUG: Foot(178.5,122.1,7.5)->Screen(2127.3,686.0) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(175.8,122.1,7.5)->Screen(2111.4,685.7) Visible:Y
W2S DEBUG: Foot(175.8,122.1,7.5)->Screen(2111.4,685.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.836 0.008 0.549 0.549]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 61.10, 172.51, 5.50
W2S DEBUG: Head(172.8,121.8,7.5)->Screen(2089.4,685.4) Visible:Y
W2S DEBUG: Foot(172.8,121.8,7.5)->Screen(2089.4,685.4) Visible:Y
W2S DEBUG: Head(171.2,120.9,8.5)->Screen(2068.4,672.9) Visible:Y
W2S DEBUG: Foot(171.2,120.9,8.5)->Screen(2068.4,672.9) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(171.1,119.8,8.5)->Screen(2053.2,673.3) Visible:Y
W2S DEBUG: Foot(171.1,119.8,8.5)->Screen(2053.2,673.3) Visible:Y
W2S DEBUG: Head(171.1,118.0,8.5)->Screen(2030.2,673.8) Visible:Y
W2S DEBUG: Foot(171.1,118.0,8.5)->Screen(2030.2,673.8) Visible:Y
W2S DEBUG: Head(171.1,116.0,8.5)->Screen(2005.5,674.4) Visible:Y
W2S DEBUG: Foot(171.1,116.0,8.5)->Screen(2005.5,674.4) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
W2S DEBUG: Head(171.1,114.9,8.5)->Screen(1992.8,674.6) Visible:Y
W2S DEBUG: Foot(171.1,114.9,8.5)->Screen(1992.8,674.6) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 61.10, 172.51, 5.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(171.1,115.2,8.5)->Screen(1995.2,674.6) Visible:Y
W2S DEBUG: Foot(171.1,115.2,8.5)->Screen(1995.2,674.6) Visible:Y
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(171.1,115.6,8.5)->Screen(2000.7,674.5) Visible:Y
W2S DEBUG: Foot(171.1,115.6,8.5)->Screen(2000.7,674.5) Visible:Y
W2S DEBUG: Head(171.1,116.1,8.5)->Screen(2006.1,674.3) Visible:Y
W2S DEBUG: Foot(171.1,116.1,8.5)->Screen(2006.1,674.3) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(171.1,116.5,8.5)->Screen(2011.1,674.2) Visible:Y
W2S DEBUG: Foot(171.1,116.5,8.5)->Screen(2011.1,674.2) Visible:Y
W2S DEBUG: Head(171.1,116.9,8.5)->Screen(2015.9,674.1) Visible:Y
W2S DEBUG: Foot(171.1,116.9,8.5)->Screen(2015.9,674.1) Visible:Y
W2S DEBUG: Head(171.1,117.2,8.5)->Screen(2020.5,674.0) Visible:Y
W2S DEBUG: Foot(171.1,117.2,8.5)->Screen(2020.5,674.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.836 0.008 0.549 0.549]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 61.10, 172.51, 5.50
W2S DEBUG: Head(171.1,117.6,8.5)->Screen(2025.4,673.9) Visible:Y
W2S DEBUG: Foot(171.1,117.6,8.5)->Screen(2025.4,673.9) Visible:Y
W2S DEBUG: Head(171.1,118.0,8.5)->Screen(2030.5,673.8) Visible:Y
W2S DEBUG: Foot(171.1,118.0,8.5)->Screen(2030.5,673.8) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(171.1,118.6,8.5)->Screen(2038.3,673.6) Visible:Y
W2S DEBUG: Foot(171.1,118.6,8.5)->Screen(2038.3,673.6) Visible:Y
W2S DEBUG: Head(171.1,119.5,8.5)->Screen(2049.7,673.3) Visible:Y
W2S DEBUG: Foot(171.1,119.5,8.5)->Screen(2049.7,673.3) Visible:Y
W2S DEBUG: Head(171.1,120.3,8.5)->Screen(2059.7,673.1) Visible:Y
W2S DEBUG: Foot(171.1,120.3,8.5)->Screen(2059.7,673.1) Visible:Y
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(171.1,121.0,8.5)->Screen(2069.3,672.9) Visible:Y
W2S DEBUG: Foot(171.1,121.0,8.5)->Screen(2069.3,672.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 61.10, 172.51, 5.50
W2S DEBUG: Head(171.1,121.4,8.5)->Screen(2073.4,672.8) Visible:Y
W2S DEBUG: Foot(171.1,121.4,8.5)->Screen(2073.4,672.8) Visible:Y
W2S DEBUG: Head(171.1,120.2,8.5)->Screen(2058.4,673.1) Visible:Y
W2S DEBUG: Foot(171.1,120.2,8.5)->Screen(2058.4,673.1) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.836 0.008 0.549 0.549]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 61.10, 172.51, 5.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FDB0428
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FDB0858
Entity[4]: 0x0FDB0C88
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 61.10, 172.51, 5.50
ENTITY DEBUG [4]: 0x0FDB0C88 - H:34 T:0 A:N
W2S DEBUG: Head(0.0,0.0,0.2)->Screen(-404.3,771.3) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.2)->Screen(-404.3,771.3) Visible:N
W2S DEBUG: Head(180.4,73.6,3.5)->Screen(184.3,720.0) Visible:Y
W2S DEBUG: Foot(180.4,73.6,3.5)->Screen(184.3,720.0) Visible:Y
W2S DEBUG: Head(178.9,74.7,3.5)->Screen(277.1,720.0) Visible:Y
W2S DEBUG: Foot(178.9,74.7,3.5)->Screen(277.1,720.0) Visible:Y
W2S DEBUG: Head(177.5,76.3,4.5)->Screen(368.9,684.6) Visible:Y
W2S DEBUG: Foot(177.5,76.3,4.5)->Screen(368.9,684.6) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE58A90
Entity[2]: 0x0FE99778
Entity[3]: 0x0FE99BA8
Entity[4]: 0x0FD2B920
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.996 0.000 -0.087 -0.087]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.00, 38.00, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(176.3,78.3,5.5)->Screen(452.2,653.2) Visible:Y
W2S DEBUG: Foot(176.3,78.3,5.5)->Screen(452.2,653.2) Visible:Y
W2S DEBUG: Head(176.1,80.2,6.5)->Screen(494.7,624.5) Visible:Y
W2S DEBUG: Foot(176.1,80.2,6.5)->Screen(494.7,624.5) Visible:Y
W2S DEBUG: Head(175.6,82.3,7.5)->Screen(542.5,598.9) Visible:Y
W2S DEBUG: Foot(175.6,82.3,7.5)->Screen(542.5,598.9) Visible:Y
ENTITY DEBUG [4]: 0x0FD2B920 - H:100 T:0 A:N
W2S DEBUG: Head(176.1,83.8,8.5)->Screen(548.9,573.8) Visible:Y
W2S DEBUG: Foot(176.1,83.8,8.5)->Screen(548.9,573.8) Visible:Y
W2S DEBUG: Head(176.5,84.5,8.5)->Screen(547.3,576.1) Visible:Y
W2S DEBUG: Foot(176.5,84.5,8.5)->Screen(547.3,576.1) Visible:Y
W2S DEBUG: Head(176.7,85.8,9.5)->Screen(556.8,552.0) Visible:Y
W2S DEBUG: Foot(176.7,85.8,9.5)->Screen(556.8,552.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE58A90
Entity[2]: 0x0FE99778
Entity[3]: 0x0FE99BA8
Entity[4]: 0x0FD2B920
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.00, 38.00, 3.50
W2S DEBUG: Head(176.8,87.1,10.0)->Screen(573.5,544.3) Visible:Y
W2S DEBUG: Foot(176.8,87.1,10.0)->Screen(573.5,544.3) Visible:Y
W2S DEBUG: Head(176.8,87.7,10.5)->Screen(580.4,531.7) Visible:Y
W2S DEBUG: Foot(176.8,87.7,10.5)->Screen(580.4,531.7) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(176.6,89.1,10.7)->Screen(601.7,532.2) Visible:Y
W2S DEBUG: Foot(176.6,89.1,10.7)->Screen(601.7,532.2) Visible:Y
W2S DEBUG: Head(176.2,90.6,11.5)->Screen(629.9,517.5) Visible:Y
W2S DEBUG: Foot(176.2,90.6,11.5)->Screen(629.9,517.5) Visible:Y
ENTITY DEBUG [4]: 0x0FD2B920 - H:100 T:0 A:N
W2S DEBUG: Head(176.1,91.5,12.5)->Screen(641.8,496.3) Visible:Y
W2S DEBUG: Foot(176.1,91.5,12.5)->Screen(641.8,496.3) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
W2S DEBUG: Head(176.1,92.5,12.5)->Screen(651.0,500.3) Visible:Y
W2S DEBUG: Foot(176.1,92.5,12.5)->Screen(651.0,500.3) Visible:Y
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE58A90
Entity[2]: 0x0FE99778
Entity[3]: 0x0FE99BA8
Entity[4]: 0x0FD2B920
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.996 0.000 -0.087 -0.087]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.00, 38.00, 3.50
W2S DEBUG: Head(176.0,94.0,13.5)->Screen(669.2,482.9) Visible:Y
W2S DEBUG: Foot(176.0,94.0,13.5)->Screen(669.2,482.9) Visible:Y
W2S DEBUG: Head(175.8,95.1,13.7)->Screen(683.3,482.1) Visible:Y
W2S DEBUG: Foot(175.8,95.1,13.7)->Screen(683.3,482.1) Visible:Y
W2S DEBUG: Head(175.1,96.7,14.5)->Screen(712.5,471.9) Visible:Y
W2S DEBUG: Foot(175.1,96.7,14.5)->Screen(712.5,471.9) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(174.9,97.6,14.5)->Screen(725.6,475.8) Visible:Y
W2S DEBUG: Foot(174.9,97.6,14.5)->Screen(725.6,475.8) Visible:Y
W2S DEBUG: Head(175.0,99.0,14.5)->Screen(732.9,481.4) Visible:Y
W2S DEBUG: Foot(175.0,99.0,14.5)->Screen(732.9,481.4) Visible:Y
W2S DEBUG: Head(176.0,100.6,14.5)->Screen(721.6,487.3) Visible:Y
W2S DEBUG: Foot(176.0,100.6,14.5)->Screen(721.6,487.3) Visible:Y
ENTITY DEBUG [4]: 0x0FD2B920 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE58A90
Entity[2]: 0x0FE99778
Entity[3]: 0x0FE99BA8
Entity[4]: 0x0FD2B920
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.00, 38.00, 3.50
W2S DEBUG: Head(177.5,101.6,14.5)->Screen(696.4,490.5) Visible:Y
W2S DEBUG: Foot(177.5,101.6,14.5)->Screen(696.4,490.5) Visible:Y
W2S DEBUG: Head(178.2,101.7,14.5)->Screen(683.3,490.7) Visible:Y
W2S DEBUG: Foot(178.2,101.7,14.5)->Screen(683.3,490.7) Visible:Y
W2S DEBUG: Head(178.2,100.3,14.5)->Screen(672.7,485.6) Visible:Y
W2S DEBUG: Foot(178.2,100.3,14.5)->Screen(672.7,485.6) Visible:Y
W2S DEBUG: Head(177.5,98.5,14.5)->Screen(673.6,478.7) Visible:Y
W2S DEBUG: Foot(177.5,98.5,14.5)->Screen(673.6,478.7) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE58A90
Entity[2]: 0x0FE99778
Entity[3]: 0x0FE99BA8
Entity[4]: 0x11D93290
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.719 0.000 -0.695 -0.695]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 174.00, 53.00, 3.50
ENTITY DEBUG [4]: 0x11D93290 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE58A90
Entity[2]: 0x0FE99778
Entity[3]: 0x0FE99BA8
Entity[4]: 0x11D93290
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 174.00, 53.00, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x11D93290 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE58A90
Entity[2]: 0x0FE99778
Entity[3]: 0x0FE99BA8
Entity[4]: 0x11D93290
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.719 0.000 -0.695 -0.695]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 174.00, 53.00, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x11D93290 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE58A90
Entity[2]: 0x0FE99778
Entity[3]: 0x0FE99BA8
Entity[4]: 0x11D93290
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 174.00, 53.00, 3.50
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x11D93290 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE58A90
Entity[2]: 0x0FE99778
Entity[3]: 0x0FE99BA8
Entity[4]: 0x11D93290
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.719 0.000 -0.695 -0.695]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 174.00, 53.00, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 168.00, 162.00, -0.06
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.294 0.540 0.907 0.906]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 168.00, 162.00, -0.06
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 174.49, 161.32, -4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.547 -0.054 -0.837 -0.836]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 181.23, 152.63, -3.51
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 179.74, 145.17, -4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.799 -0.051 -0.602 -0.601]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 177.61, 133.12, -4.50
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.17, 119.96, -1.40
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.462 -0.127 -0.884 -0.884]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 169.98, 105.50, 4.74
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.59, 103.81, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.985 -0.046 -0.173 -0.173]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.31, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.47, 97.10, 4.50
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.989 -0.033 -0.146 -0.146]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.22, 97.10, 3.45
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.87, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.989 -0.033 -0.146 -0.146]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.87, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.87, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.989 -0.033 -0.146 -0.146]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.87, 97.10, 4.50
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.87, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.989 -0.033 -0.146 -0.146]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.87, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 155.87, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.989 -0.033 -0.146 -0.146]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.14, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
W2S DEBUG: Head(129.7,153.9,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(129.7,153.9,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(114.6,172.5,13.9)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(114.6,172.5,13.9)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.693 0.000 -0.147 -0.147]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(109.1,170.7,8.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(109.1,170.7,8.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(132.1,151.2,8.6)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(132.1,151.2,8.6)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(109.1,169.7,8.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(109.1,169.7,8.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
W2S DEBUG: Head(109.2,170.9,10.7)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(109.2,170.9,10.7)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(109.6,172.3,9.1)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(109.6,172.3,9.1)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.693 0.000 -0.147 -0.147]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(129.4,147.1,11.3)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(129.4,147.1,11.3)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
W2S DEBUG: Head(119.9,173.9,9.2)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(119.9,173.9,9.2)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(119.9,173.9,9.2)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(119.9,173.9,9.2)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.693 0.000 -0.147 -0.147]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
W2S DEBUG: Head(132.1,157.5,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(132.1,157.5,4.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
W2S DEBUG: Head(133.9,160.4,7.7)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(133.9,160.4,7.7)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
W2S DEBUG: Head(133.9,160.5,6.9)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(133.9,160.5,6.9)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.693 0.000 -0.147 -0.147]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
W2S DEBUG: Head(130.6,150.6,9.6)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(130.6,150.6,9.6)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
W2S DEBUG: Head(130.5,149.1,8.6)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(130.5,149.1,8.6)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(131.2,147.9,11.4)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(131.2,147.9,11.4)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(132.8,147.1,10.3)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(132.8,147.1,10.3)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.693 0.000 -0.147 -0.147]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
W2S DEBUG: Head(160.1,178.8,-5.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(160.1,178.8,-5.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(159.9,180.3,-4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(159.9,180.3,-4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(134.0,147.1,10.7)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(134.0,147.1,10.7)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
W2S DEBUG: Head(130.1,147.4,11.1)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(130.1,147.4,11.1)->Screen(0.0,0.0) Visible:N
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.693 0.000 -0.147 -0.147]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
W2S DEBUG: Head(144.6,182.7,-2.1)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(144.6,182.7,-2.1)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(125.1,154.4,11.2)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(125.1,154.4,11.2)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
W2S DEBUG: Head(143.5,180.3,-2.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(143.5,180.3,-2.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(140.3,178.1,-2.7)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(140.3,178.1,-2.7)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
W2S DEBUG: Head(140.9,178.2,-2.7)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(140.9,178.2,-2.7)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.693 0.000 -0.147 -0.147]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
W2S DEBUG: Head(127.3,150.7,10.3)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(127.3,150.7,10.3)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(142.8,182.0,-3.3)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(142.8,182.0,-3.3)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
W2S DEBUG: Head(155.8,181.5,-4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(155.8,181.5,-4.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(158.2,180.8,-4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(158.2,180.8,-4.5)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.693 0.000 -0.147 -0.147]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
W2S DEBUG: Head(163.3,178.2,-5.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(163.3,178.2,-5.0)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 156.46, 97.10, 4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.386 0.088 -0.922 -0.921]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.99, 172.00, -4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 168.98, 169.82, -4.06
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.994 -0.015 -0.107 -0.107]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 181.05, 168.41, -4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 176.08, 156.70, -4.50
ENTITY DEBUG [4]: 0x0FD2BD50 - H:40 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.944 0.127 0.321 0.321]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 178.80, 147.80, -4.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FD2BD50 - H:40 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 180.02, 155.90, -4.00
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:-20 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.454 0.208 -0.884 -0.883]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 172.33, 161.44, -4.50
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:-20 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 177.45, 169.83, -4.25
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.583 0.176 -0.807 -0.807]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 177.61, 169.95, -4.25
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [4]: 0x0FD2BD50 - H:-20 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 177.61, 169.95, -4.25
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [4]: 0x0FD2BD50 - H:-20 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.581 0.177 -0.808 -0.808]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 177.61, 169.95, -4.25
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FD2BD50 - H:-20 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x11E57830
Entity[2]: 0x0FE58A90
Entity[3]: 0x0FD2B920
Entity[4]: 0x0FD2BD50
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 177.61, 169.95, -4.25
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
