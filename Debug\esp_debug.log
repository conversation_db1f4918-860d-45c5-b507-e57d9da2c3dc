AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window!
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 268.15, 197.10, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(311.0,213.0,12.5)->Screen(1356.6,-0.1) Visible:N
W2S DEBUG: Foot(311.0,213.0,12.5)->Screen(1356.6,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 268.15, 197.10, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(302.0,205.2,12.5)->Screen(1359.4,-0.1) Visible:N
W2S DEBUG: Foot(302.0,205.2,12.5)->Screen(1359.4,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 268.15, 197.10, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(299.3,208.5,12.5)->Screen(1358.2,-0.1) Visible:N
W2S DEBUG: Foot(299.3,208.5,12.5)->Screen(1358.2,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(288.9,198.2,12.5)->Screen(1362.0,-0.1) Visible:N
W2S DEBUG: Foot(288.9,198.2,12.5)->Screen(1362.0,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(285.0,202.7,12.5)->Screen(1360.3,-0.1) Visible:N
W2S DEBUG: Foot(285.0,202.7,12.5)->Screen(1360.3,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(284.8,208.1,12.5)->Screen(1358.3,-0.1) Visible:N
W2S DEBUG: Foot(284.8,208.1,12.5)->Screen(1358.3,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(288.5,207.6,12.5)->Screen(1358.5,-0.1) Visible:N
W2S DEBUG: Foot(288.5,207.6,12.5)->Screen(1358.5,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(293.2,206.8,12.5)->Screen(1358.8,-0.1) Visible:N
W2S DEBUG: Foot(293.2,206.8,12.5)->Screen(1358.8,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(298.2,207.7,12.5)->Screen(1358.5,-0.1) Visible:N
W2S DEBUG: Foot(298.2,207.7,12.5)->Screen(1358.5,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(302.9,208.2,12.5)->Screen(1358.3,-0.1) Visible:N
W2S DEBUG: Foot(302.9,208.2,12.5)->Screen(1358.3,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(304.7,214.3,12.5)->Screen(1356.2,-0.1) Visible:N
W2S DEBUG: Foot(304.7,214.3,12.5)->Screen(1356.2,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(305.9,219.5,12.5)->Screen(1354.5,-0.1) Visible:N
W2S DEBUG: Foot(305.9,219.5,12.5)->Screen(1354.5,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(305.2,225.9,12.5)->Screen(1352.5,-0.1) Visible:N
W2S DEBUG: Foot(305.2,225.9,12.5)->Screen(1352.5,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(307.0,234.0,12.5)->Screen(1350.1,-0.1) Visible:N
W2S DEBUG: Foot(307.0,234.0,12.5)->Screen(1350.1,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(308.7,242.0,12.5)->Screen(1347.9,-0.1) Visible:N
W2S DEBUG: Foot(308.7,242.0,12.5)->Screen(1347.9,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10BA6010
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
VIEWMATRIX DEBUG: Current offset 0x10F580
VM[0-3]: 0.000 0.000 0.000 0.000
VM[12-15]: 0.000 0.000 0.000 0.000
ALT1 VM[0-3]: 0.000 0.000 0.000 0.000
ALT2 VM[0-3]: 0.000 0.000 0.000 0.000
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 102.00, 274.00, 12.50
ENTITY DEBUG [4]: 0x10A01B78 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(303.4,241.1,12.5)->Screen(1348.1,-0.1) Visible:N
W2S DEBUG: Foot(303.4,241.1,12.5)->Screen(1348.1,-0.1) Visible:N
W2S DEBUG: Screen size: 2560x1440
