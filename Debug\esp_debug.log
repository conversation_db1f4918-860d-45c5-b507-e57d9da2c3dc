AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window! Size: 2560x1440
PLAYER COUNT DEBUG: Read playerCount = 6
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=6, LocalPlayer=0x02C8A158
VIEWMATRIX DEBUG: Valid=Y [-0.684 -0.010 0.730 0.729]
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.684 -0.010 0.730 0.729]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 118.00, 107.00, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(165.9,160.9,2.8)->Screen(1396.5,743.1) Visible:Y
W2S DEBUG: Foot(165.9,160.9,2.8)->Screen(1396.5,743.1) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N, ShowEnemies: Y, ShowTeammates: Y
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
W2S DEBUG: Head(164.9,161.9,0.1)->Screen(1421.4,790.4) Visible:Y
W2S DEBUG: Foot(164.9,161.9,0.1)->Screen(1421.4,790.4) Visible:Y
W2S DEBUG: Head(163.7,163.3,1.3)->Screen(1454.0,769.2) Visible:Y
W2S DEBUG: Foot(163.7,163.3,1.3)->Screen(1454.0,769.2) Visible:Y
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
W2S DEBUG: Head(162.5,165.1,2.4)->Screen(1491.1,749.9) Visible:Y
W2S DEBUG: Foot(162.5,165.1,2.4)->Screen(1491.1,749.9) Visible:Y
W2S DEBUG: Head(161.2,167.0,2.1)->Screen(1530.9,754.1) Visible:Y
W2S DEBUG: Foot(161.2,167.0,2.1)->Screen(1530.9,754.1) Visible:Y
W2S DEBUG: Head(159.9,168.9,0.8)->Screen(1569.9,778.1) Visible:Y
W2S DEBUG: Foot(159.9,168.9,0.8)->Screen(1569.9,778.1) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 118.00, 107.00, 3.50
W2S DEBUG: Head(158.6,171.1,-1.7)->Screen(1610.6,821.0) Visible:Y
W2S DEBUG: Foot(158.6,171.1,-1.7)->Screen(1610.6,821.0) Visible:Y
W2S DEBUG: Head(157.6,173.6,-1.6)->Screen(1649.8,817.3) Visible:Y
W2S DEBUG: Foot(157.6,173.6,-1.6)->Screen(1649.8,817.3) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(157.0,176.4,-0.5)->Screen(1683.3,797.6) Visible:Y
W2S DEBUG: Foot(157.0,176.4,-0.5)->Screen(1683.3,797.6) Visible:Y
W2S DEBUG: Head(156.3,178.7,-0.5)->Screen(1714.3,796.5) Visible:Y
W2S DEBUG: Foot(156.3,178.7,-0.5)->Screen(1714.3,796.5) Visible:Y
W2S DEBUG: Head(155.3,179.8,-0.4)->Screen(1739.2,794.6) Visible:Y
W2S DEBUG: Foot(155.3,179.8,-0.4)->Screen(1739.2,794.6) Visible:Y
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
W2S DEBUG: Head(155.0,179.8,-0.4)->Screen(1743.3,794.8) Visible:Y
W2S DEBUG: Foot(155.0,179.8,-0.4)->Screen(1743.3,794.8) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.684 -0.010 0.730 0.729]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 118.00, 107.00, 3.50
W2S DEBUG: Head(155.1,179.8,-0.4)->Screen(1741.5,794.7) Visible:Y
W2S DEBUG: Foot(155.1,179.8,-0.4)->Screen(1741.5,794.7) Visible:Y
W2S DEBUG: Head(155.7,179.8,-0.4)->Screen(1731.5,794.3) Visible:Y
W2S DEBUG: Foot(155.7,179.8,-0.4)->Screen(1731.5,794.3) Visible:Y
W2S DEBUG: Head(156.7,179.8,-0.5)->Screen(1717.2,795.8) Visible:Y
W2S DEBUG: Foot(156.7,179.8,-0.5)->Screen(1717.2,795.8) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(157.8,179.9,-0.5)->Screen(1700.2,795.1) Visible:Y
W2S DEBUG: Foot(157.8,179.9,-0.5)->Screen(1700.2,795.1) Visible:Y
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N, ShowEnemies: Y, ShowTeammates: Y
W2S DEBUG: Head(159.5,179.9,-0.5)->Screen(1675.5,794.1) Visible:Y
W2S DEBUG: Foot(159.5,179.9,-0.5)->Screen(1675.5,794.1) Visible:Y
W2S DEBUG: Head(161.2,179.9,-0.5)->Screen(1650.6,793.1) Visible:Y
W2S DEBUG: Foot(161.2,179.9,-0.5)->Screen(1650.6,793.1) Visible:Y
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 118.00, 107.00, 3.50
W2S DEBUG: Head(163.0,179.9,-0.5)->Screen(1626.8,792.1) Visible:Y
W2S DEBUG: Foot(163.0,179.9,-0.5)->Screen(1626.8,792.1) Visible:Y
W2S DEBUG: Head(164.8,179.9,-0.4)->Screen(1602.4,789.2) Visible:Y
W2S DEBUG: Foot(164.8,179.9,-0.4)->Screen(1602.4,789.2) Visible:Y
W2S DEBUG: Head(166.9,179.9,-0.4)->Screen(1575.1,788.2) Visible:Y
W2S DEBUG: Foot(166.9,179.9,-0.4)->Screen(1575.1,788.2) Visible:Y
W2S DEBUG: Head(169.1,179.9,-0.2)->Screen(1547.3,784.3) Visible:Y
W2S DEBUG: Foot(169.1,179.9,-0.2)->Screen(1547.3,784.3) Visible:Y
W2S DEBUG: Head(171.4,179.6,-0.2)->Screen(1349.0,720.1) Visible:Y
W2S DEBUG: Foot(171.4,179.6,-0.2)->Screen(1349.0,720.1) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(173.5,179.1,-0.4)->Screen(1349.6,720.2) Visible:Y
W2S DEBUG: Foot(173.5,179.1,-0.4)->Screen(1349.6,720.2) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.397 0.300 0.903 0.902]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.61, 106.63, 3.50
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
PLAYER COUNT DEBUG: Read playerCount = 6
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=6, LocalPlayer=0x02C8A158
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.621 0.228 0.774 0.773]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N, ShowEnemies: Y, ShowTeammates: Y
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.621 0.228 0.774 0.773]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
PLAYER COUNT DEBUG: Read playerCount = 6
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=6, LocalPlayer=0x02C8A158
VIEWMATRIX DEBUG: Valid=Y [-0.621 0.228 0.774 0.773]
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.621 0.228 0.774 0.773]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N, ShowEnemies: Y, ShowTeammates: Y
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.621 0.231 0.774 0.773]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(0.0,0.0,0.0)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.621 0.231 0.774 0.773]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N, ShowEnemies: Y, ShowTeammates: Y
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
PLAYER COUNT DEBUG: Read playerCount = 6
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=6, LocalPlayer=0x02C8A158
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.621 0.231 0.774 0.773]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N, ShowEnemies: Y, ShowTeammates: Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.621 0.231 0.774 0.773]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=6
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE19BD0
Entity[2]: 0x0FE38010
Entity[3]: 0x0FE4C5C8
Entity[4]: 0x0FE6A010
Players: 6, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 6 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 117.60, 106.62, 3.50
ENTITY DEBUG [4]: 0x0FE6A010 - H:100 T:0 A:N
