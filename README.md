# Assault Cube External ESP

A transparent overlay ESP for Assault Cube 1.2 with ImGui menu interface.

## Features

- **Transparent Overlay**: Fully transparent window that overlays on top of Assault Cube
- **Toggleable Menu**: Press INSERT to show/hide the ESP configuration menu
- **Click-through**: When menu is hidden, the overlay is click-through so you can play normally
- **ESP Features**:
  - Player boxes with customizable colors
  - Health bars (green to red gradient)
  - Armor bars (blue)
  - Player names
  - Distance display
  - Team identification
  - Snaplines (configurable position: top, center, bottom)
  - Head dots
  - Weapon and ammo information
  - Player state (firing detection)

## Usage

1. **Start Assault Cube 1.2** first
2. **Run AssaultCubeESP.exe** as Administrator (required for memory access)
3. The overlay will automatically attach to the game process
4. **Press INSERT** to toggle the ESP menu on/off
5. Configure ESP settings in the menu
6. **Press INSERT again** to hide the menu and make the overlay click-through

## Controls

- **INSERT**: Toggle ESP menu visibility
- **Mouse**: When menu is visible, you can click and interact with the ESP settings
- **ESC**: Close the application

## ESP Settings

### Visual Options
- **Enable ESP**: Master toggle for all ESP features
- **Boxes**: Draw bounding boxes around players
- **Health Bar**: Show health as a colored bar (left side of box)
- **Names**: Display player names
- **Distance**: Show distance to players
- **Team**: Display team information
- **Armor**: Show armor as a blue bar (right side of box)
- **Snaplines**: Draw lines from screen edge to players
- **Head Dot**: Draw a dot at player head position

### Advanced Options
- **Weapons**: Show current weapon information
- **Ammo**: Display ammunition counts
- **State**: Show player states (firing, etc.)

### Visual Settings
- **Box Thickness**: Adjust thickness of ESP boxes (0.5-5.0)
- **Snapline Thickness**: Adjust thickness of snaplines (0.5-3.0)
- **Snapline Position**: Choose where snaplines originate (Top/Center/Bottom)

### Colors
- **Enemy Color**: Color for enemy players (default: red)
- **Team Color**: Color for team players (default: green)
- **Text Color**: Color for text information (default: white)
- **Snapline Color**: Color for snaplines (default: yellow)

## Technical Details

- **External Process**: Reads game memory externally, no DLL injection
- **DirectX 11**: Uses D3D11 for hardware-accelerated rendering
- **ImGui**: Modern immediate mode GUI for the menu interface
- **Memory Safe**: Includes error handling for memory operations
- **Auto-Attach**: Automatically finds and attaches to Assault Cube process

## Requirements

- Windows 10/11
- Assault Cube 1.2
- Administrator privileges (for memory access)
- DirectX 11 compatible graphics card

## Notes

- The ESP will automatically wait for Assault Cube to start if it's not running
- If Assault Cube closes, you'll need to restart the ESP
- The overlay is designed to be minimally intrusive when the menu is hidden
- All ESP features can be toggled on/off individually

## Troubleshooting

- **"Failed to attach to ac_client.exe"**: Make sure Assault Cube is running and the ESP is run as Administrator
- **Black screen**: Try running in windowed mode or check DirectX compatibility
- **Menu not responding**: Make sure the menu is visible (press INSERT) before trying to interact
