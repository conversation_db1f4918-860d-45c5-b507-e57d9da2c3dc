AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window! Size: 2560x1440
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.317 0.236 0.940 0.939]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 13.76, 163.70, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(107.4,15.4,4.5)->Screen(231.4,615.6) Visible:Y
W2S DEBUG: Foot(107.4,15.4,4.5)->Screen(231.4,615.6) Visible:Y
W2S DEBUG: Head(107.8,18.7,4.5)->Screen(256.7,616.0) Visible:Y
W2S DEBUG: Foot(107.8,18.7,4.5)->Screen(256.7,616.0) Visible:Y
W2S DEBUG: Head(109.6,21.9,4.5)->Screen(296.3,615.6) Visible:Y
W2S DEBUG: Foot(109.6,21.9,4.5)->Screen(296.3,615.6) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(110.6,25.1,4.5)->Screen(328.2,615.6) Visible:Y
W2S DEBUG: Foot(110.6,25.1,4.5)->Screen(328.2,615.6) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 13.63, 163.74, 12.50
W2S DEBUG: Head(111.7,28.3,4.5)->Screen(1695.4,586.4) Visible:Y
W2S DEBUG: Foot(111.7,28.3,4.5)->Screen(1695.4,586.4) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(112.9,31.4,4.5)->Screen(1722.8,587.0) Visible:Y
W2S DEBUG: Foot(112.9,31.4,4.5)->Screen(1722.8,587.0) Visible:Y
W2S DEBUG: Head(114.9,33.2,6.2)->Screen(1627.2,618.1) Visible:Y
W2S DEBUG: Foot(114.9,33.2,6.2)->Screen(1627.2,618.1) Visible:Y
W2S DEBUG: Head(114.9,34.3,7.7)->Screen(1395.6,601.1) Visible:Y
W2S DEBUG: Foot(114.9,34.3,7.7)->Screen(1395.6,601.1) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.856 0.114 0.514 0.514]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.15, 161.71, 12.50
W2S DEBUG: Head(114.9,33.9,6.4)->Screen(1400.6,610.6) Visible:Y
W2S DEBUG: Foot(114.9,33.9,6.4)->Screen(1400.6,610.6) Visible:Y
W2S DEBUG: Head(114.9,32.6,6.0)->Screen(1393.2,613.7) Visible:Y
W2S DEBUG: Foot(114.9,32.6,6.0)->Screen(1393.2,613.7) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(114.1,30.8,7.4)->Screen(1379.5,601.4) Visible:Y
W2S DEBUG: Foot(114.1,30.8,7.4)->Screen(1379.5,601.4) Visible:Y
W2S DEBUG: Head(113.2,29.7,7.0)->Screen(1368.6,605.0) Visible:Y
W2S DEBUG: Foot(113.2,29.7,7.0)->Screen(1368.6,605.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(112.4,28.7,5.9)->Screen(1358.8,613.8) Visible:Y
W2S DEBUG: Foot(112.4,28.7,5.9)->Screen(1358.8,613.8) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(111.8,27.6,4.5)->Screen(1349.5,624.3) Visible:Y
W2S DEBUG: Foot(111.8,27.6,4.5)->Screen(1349.5,624.3) Visible:Y
W2S DEBUG: Head(111.1,26.3,4.5)->Screen(1341.6,624.1) Visible:Y
W2S DEBUG: Foot(111.1,26.3,4.5)->Screen(1341.6,624.1) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(110.1,25.2,4.5)->Screen(1330.1,623.9) Visible:Y
W2S DEBUG: Foot(110.1,25.2,4.5)->Screen(1330.1,623.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.856 0.113 0.513 0.512]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(108.8,24.5,4.5)->Screen(1318.3,624.0) Visible:Y
W2S DEBUG: Foot(108.8,24.5,4.5)->Screen(1318.3,624.0) Visible:Y
W2S DEBUG: Head(107.3,24.5,5.3)->Screen(1308.1,617.7) Visible:Y
W2S DEBUG: Foot(107.3,24.5,5.3)->Screen(1308.1,617.7) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(107.1,24.9,6.9)->Screen(1309.0,605.7) Visible:Y
W2S DEBUG: Foot(107.1,24.9,6.9)->Screen(1309.0,605.7) Visible:Y
W2S DEBUG: Head(107.1,25.6,7.7)->Screen(1312.0,599.5) Visible:Y
W2S DEBUG: Foot(107.1,25.6,7.7)->Screen(1312.0,599.5) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(107.1,26.5,7.7)->Screen(1316.2,599.6) Visible:Y
W2S DEBUG: Foot(107.1,26.5,7.7)->Screen(1316.2,599.6) Visible:Y
W2S DEBUG: Head(107.1,27.7,7.0)->Screen(1321.3,605.5) Visible:Y
W2S DEBUG: Foot(107.1,27.7,7.0)->Screen(1321.3,605.5) Visible:Y
W2S DEBUG: Head(107.1,29.0,5.6)->Screen(1327.1,616.6) Visible:Y
W2S DEBUG: Foot(107.1,29.0,5.6)->Screen(1327.1,616.6) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(107.1,30.4,5.1)->Screen(1332.9,621.8) Visible:Y
W2S DEBUG: Foot(107.1,30.4,5.1)->Screen(1332.9,621.8) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.856 0.113 0.513 0.512]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(107.3,31.8,6.6)->Screen(1340.4,609.4) Visible:Y
W2S DEBUG: Foot(107.3,31.8,6.6)->Screen(1340.4,609.4) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(107.7,33.1,7.4)->Screen(1349.8,603.4) Visible:Y
W2S DEBUG: Foot(107.7,33.1,7.4)->Screen(1349.8,603.4) Visible:Y
W2S DEBUG: Head(108.5,34.2,7.4)->Screen(1360.1,603.7) Visible:Y
W2S DEBUG: Foot(108.5,34.2,7.4)->Screen(1360.1,603.7) Visible:Y
W2S DEBUG: Head(109.3,35.4,6.7)->Screen(1371.1,609.7) Visible:Y
W2S DEBUG: Foot(109.3,35.4,6.7)->Screen(1371.1,609.7) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(110.2,36.4,5.3)->Screen(1382.6,621.0) Visible:Y
W2S DEBUG: Foot(110.2,36.4,5.3)->Screen(1382.6,621.0) Visible:Y
W2S DEBUG: Head(111.2,37.4,4.5)->Screen(1394.1,628.0) Visible:Y
W2S DEBUG: Foot(111.2,37.4,4.5)->Screen(1394.1,628.0) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(112.5,38.3,5.4)->Screen(1407.7,620.2) Visible:Y
W2S DEBUG: Foot(112.5,38.3,5.4)->Screen(1407.7,620.2) Visible:Y
W2S DEBUG: Head(113.8,39.2,5.5)->Screen(1420.6,619.7) Visible:Y
W2S DEBUG: Foot(113.8,39.2,5.5)->Screen(1420.6,619.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.856 0.113 0.513 0.512]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(114.9,40.4,4.8)->Screen(1433.9,626.1) Visible:Y
W2S DEBUG: Foot(114.9,40.4,4.8)->Screen(1433.9,626.1) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(115.9,41.6,4.5)->Screen(1447.3,628.6) Visible:Y
W2S DEBUG: Foot(115.9,41.6,4.5)->Screen(1447.3,628.6) Visible:Y
W2S DEBUG: Head(117.2,42.6,4.5)->Screen(1460.9,628.6) Visible:Y
W2S DEBUG: Foot(117.2,42.6,4.5)->Screen(1460.9,628.6) Visible:Y
RECOIL DEBUG: ViewPunch values: 31.091, -7.100, 0.000
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(118.6,43.0,4.5)->Screen(2340.3,806.4) Visible:Y
W2S DEBUG: Foot(118.6,43.0,4.5)->Screen(2340.3,806.4) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(120.2,43.0,4.5)->Screen(2357.2,806.4) Visible:Y
W2S DEBUG: Foot(120.2,43.0,4.5)->Screen(2357.2,806.4) Visible:Y
W2S DEBUG: Head(121.8,42.7,4.5)->Screen(2371.3,806.1) Visible:Y
W2S DEBUG: Foot(121.8,42.7,4.5)->Screen(2371.3,806.1) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(123.3,42.1,4.5)->Screen(2382.5,805.7) Visible:Y
W2S DEBUG: Foot(123.3,42.1,4.5)->Screen(2382.5,805.7) Visible:Y
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(124.4,41.1,4.5)->Screen(2385.1,805.0) Visible:Y
W2S DEBUG: Foot(124.4,41.1,4.5)->Screen(2385.1,805.0) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(124.4,40.3,4.5)->Screen(2378.1,804.5) Visible:Y
W2S DEBUG: Foot(124.4,40.3,4.5)->Screen(2378.1,804.5) Visible:Y
W2S DEBUG: Head(123.7,40.3,4.5)->Screen(2370.7,804.5) Visible:Y
W2S DEBUG: Foot(123.7,40.3,4.5)->Screen(2370.7,804.5) Visible:Y
W2S DEBUG: Head(123.6,40.3,4.5)->Screen(2369.5,804.5) Visible:Y
W2S DEBUG: Foot(123.6,40.3,4.5)->Screen(2369.5,804.5) Visible:Y
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(122.2,39.7,4.5)->Screen(2349.2,804.1) Visible:Y
W2S DEBUG: Foot(122.2,39.7,4.5)->Screen(2349.2,804.1) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(120.7,39.6,4.5)->Screen(2332.4,804.0) Visible:Y
W2S DEBUG: Foot(120.7,39.6,4.5)->Screen(2332.4,804.0) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(119.1,39.9,4.5)->Screen(2318.7,804.2) Visible:Y
W2S DEBUG: Foot(119.1,39.9,4.5)->Screen(2318.7,804.2) Visible:Y
W2S DEBUG: Head(117.6,40.6,4.5)->Screen(2308.3,804.7) Visible:Y
W2S DEBUG: Foot(117.6,40.6,4.5)->Screen(2308.3,804.7) Visible:Y
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(116.2,41.4,4.5)->Screen(2299.9,805.2) Visible:Y
W2S DEBUG: Foot(116.2,41.4,4.5)->Screen(2299.9,805.2) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(115.1,42.5,4.5)->Screen(2298.5,806.1) Visible:Y
W2S DEBUG: Foot(115.1,42.5,4.5)->Screen(2298.5,806.1) Visible:Y
W2S DEBUG: Head(114.8,44.0,4.5)->Screen(2307.3,807.1) Visible:Y
W2S DEBUG: Foot(114.8,44.0,4.5)->Screen(2307.3,807.1) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(115.1,45.5,4.5)->Screen(2323.6,808.2) Visible:Y
W2S DEBUG: Foot(115.1,45.5,4.5)->Screen(2323.6,808.2) Visible:Y
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(115.8,46.9,4.5)->Screen(2344.2,809.3) Visible:Y
W2S DEBUG: Foot(115.8,46.9,4.5)->Screen(2344.2,809.3) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(116.6,48.3,4.5)->Screen(2366.8,810.4) Visible:Y
W2S DEBUG: Foot(116.6,48.3,4.5)->Screen(2366.8,810.4) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(117.4,49.7,4.5)->Screen(2389.0,811.6) Visible:Y
W2S DEBUG: Foot(117.4,49.7,4.5)->Screen(2389.0,811.6) Visible:Y
W2S DEBUG: Head(117.9,51.2,4.5)->Screen(2411.3,812.8) Visible:Y
W2S DEBUG: Foot(117.9,51.2,4.5)->Screen(2411.3,812.8) Visible:Y
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(118.2,52.8,4.5)->Screen(2430.1,814.2) Visible:Y
W2S DEBUG: Foot(118.2,52.8,4.5)->Screen(2430.1,814.2) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(118.3,54.3,4.5)->Screen(2447.8,815.5) Visible:Y
W2S DEBUG: Foot(118.3,54.3,4.5)->Screen(2447.8,815.5) Visible:Y
W2S DEBUG: Head(118.3,56.0,4.5)->Screen(2466.8,817.0) Visible:Y
W2S DEBUG: Foot(118.3,56.0,4.5)->Screen(2466.8,817.0) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(118.3,57.6,3.5)->Screen(2485.8,830.9) Visible:Y
W2S DEBUG: Foot(118.3,57.6,3.5)->Screen(2485.8,830.9) Visible:Y
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(118.2,59.3,3.5)->Screen(2503.4,832.7) Visible:Y
W2S DEBUG: Foot(118.2,59.3,3.5)->Screen(2503.4,832.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(117.7,62.6,3.5)->Screen(2538.2,836.4) Visible:Y
W2S DEBUG: Foot(117.7,62.6,3.5)->Screen(2538.2,836.4) Visible:Y
W2S DEBUG: Head(119.1,65.0,3.8)->Screen(2587.6,835.5) Visible:Y
W2S DEBUG: Foot(119.1,65.0,3.8)->Screen(2587.6,835.5) Visible:Y
W2S DEBUG: Head(122.1,65.6,4.5)->Screen(2635.7,826.7) Visible:Y
W2S DEBUG: Foot(122.1,65.6,4.5)->Screen(2635.7,826.7) Visible:Y
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(122.9,66.5,7.4)->Screen(2659.7,788.6) Visible:Y
W2S DEBUG: Foot(122.9,66.5,7.4)->Screen(2659.7,788.6) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(125.0,69.2,7.4)->Screen(2729.1,791.2) Visible:N
W2S DEBUG: Foot(125.0,69.2,7.4)->Screen(2729.1,791.2) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(127.3,71.5,4.6)->Screen(2798.9,831.6) Visible:N
W2S DEBUG: Foot(127.3,71.5,4.6)->Screen(2798.9,831.6) Visible:N
W2S DEBUG: Head(129.6,73.7,7.1)->Screen(2767.1,793.1) Visible:N
W2S DEBUG: Foot(129.6,73.7,7.1)->Screen(2767.1,793.1) Visible:N
RECOIL DEBUG: ViewPunch values: 4.182, -0.455, 0.000
W2S DEBUG: Head(131.9,76.0,7.1)->Screen(2948.6,801.3) Visible:N
W2S DEBUG: Foot(131.9,76.0,7.1)->Screen(2948.6,801.3) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(134.1,78.5,4.5)->Screen(3033.2,843.3) Visible:N
W2S DEBUG: Foot(134.1,78.5,4.5)->Screen(3033.2,843.3) Visible:N
W2S DEBUG: Head(136.5,80.9,4.5)->Screen(3122.0,847.0) Visible:N
W2S DEBUG: Foot(136.5,80.9,4.5)->Screen(3122.0,847.0) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(139.3,82.2,4.5)->Screen(3197.7,849.1) Visible:N
W2S DEBUG: Foot(139.3,82.2,4.5)->Screen(3197.7,849.1) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(141.8,80.9,4.5)->Screen(3200.4,846.7) Visible:N
W2S DEBUG: Foot(141.8,80.9,4.5)->Screen(3200.4,846.7) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(143.6,78.3,4.5)->Screen(3173.3,840.9) Visible:N
W2S DEBUG: Foot(143.6,78.3,4.5)->Screen(3173.3,840.9) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(144.7,75.3,4.5)->Screen(3123.1,838.7) Visible:N
W2S DEBUG: Foot(144.7,75.3,4.5)->Screen(3123.1,838.7) Visible:N
W2S DEBUG: Head(144.5,73.1,6.5)->Screen(3070.4,804.3) Visible:N
W2S DEBUG: Foot(144.5,73.1,6.5)->Screen(3070.4,804.3) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, -0.182, 0.000
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(143.6,70.9,10.0)->Screen(3013.8,747.7) Visible:N
W2S DEBUG: Foot(143.6,70.9,10.0)->Screen(3013.8,747.7) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.2,68.1,10.1)->Screen(2948.5,751.3) Visible:N
W2S DEBUG: Foot(142.2,68.1,10.1)->Screen(2948.5,751.3) Visible:N
W2S DEBUG: Head(141.3,65.4,7.6)->Screen(2883.8,783.1) Visible:N
W2S DEBUG: Foot(141.3,65.4,7.6)->Screen(2883.8,783.1) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(141.5,65.1,4.5)->Screen(2887.1,826.2) Visible:N
W2S DEBUG: Foot(141.5,65.1,4.5)->Screen(2887.1,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(142.2,65.1,4.5)->Screen(2903.9,840.8) Visible:N
W2S DEBUG: Foot(142.2,65.1,4.5)->Screen(2903.9,840.8) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.002 0.002]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2899.9,826.0) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2899.9,826.0) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.4,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.4,826.2) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.4,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.4,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(3489.3,874.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(3489.3,874.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(3484.9,874.9) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(3484.9,874.9) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.5,65.1,4.5)->Screen(3380.7,865.0) Visible:N
W2S DEBUG: Foot(142.5,65.1,4.5)->Screen(3380.7,865.0) Visible:N
W2S DEBUG: Head(141.5,65.1,4.5)->Screen(3287.4,862.2) Visible:N
W2S DEBUG: Foot(141.5,65.1,4.5)->Screen(3287.4,862.2) Visible:N
RECOIL DEBUG: ViewPunch values: 353.846, 0.760, 0.000
W2S DEBUG: Head(142.5,65.1,4.5)->Screen(3448.6,874.7) Visible:N
W2S DEBUG: Foot(142.5,65.1,4.5)->Screen(3448.6,874.7) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.991 0.004 -0.134 -0.134]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(3455.1,870.7) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(3455.1,870.7) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(3447.2,870.3) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(3447.2,870.3) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(3454.1,865.6) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(3454.1,865.6) Visible:N
RECOIL DEBUG: ViewPunch values: 352.298, 0.719, 0.000
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.6,65.1,4.5)->Screen(2902.2,826.2) Visible:N
W2S DEBUG: Foot(142.6,65.1,4.5)->Screen(2902.2,826.2) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(141.7,65.1,4.5)->Screen(2879.0,825.8) Visible:N
W2S DEBUG: Foot(141.7,65.1,4.5)->Screen(2879.0,825.8) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Head(142.6,65.1,4.5)->Screen(2902.0,826.2) Visible:N
W2S DEBUG: Foot(142.6,65.1,4.5)->Screen(2902.0,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(142.0,65.1,4.5)->Screen(2894.4,826.2) Visible:N
W2S DEBUG: Foot(142.0,65.1,4.5)->Screen(2894.4,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(141.2,65.1,4.5)->Screen(2882.9,826.2) Visible:N
W2S DEBUG: Foot(141.2,65.1,4.5)->Screen(2882.9,826.2) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(141.1,65.1,4.5)->Screen(2881.6,826.2) Visible:N
W2S DEBUG: Foot(141.1,65.1,4.5)->Screen(2881.6,826.2) Visible:N
W2S DEBUG: Head(141.1,65.1,4.5)->Screen(2882.0,826.2) Visible:N
W2S DEBUG: Foot(141.1,65.1,4.5)->Screen(2882.0,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(142.0,65.1,4.5)->Screen(2893.2,826.2) Visible:N
W2S DEBUG: Foot(142.0,65.1,4.5)->Screen(2893.2,826.2) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Head(142.6,65.1,4.5)->Screen(2901.9,826.2) Visible:N
W2S DEBUG: Foot(142.6,65.1,4.5)->Screen(2901.9,826.2) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(142.1,65.1,4.5)->Screen(2895.5,826.2) Visible:N
W2S DEBUG: Foot(142.1,65.1,4.5)->Screen(2895.5,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(141.6,65.1,4.5)->Screen(2888.6,826.2) Visible:N
W2S DEBUG: Foot(141.6,65.1,4.5)->Screen(2888.6,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.0,65.1,4.5)->Screen(2893.2,826.2) Visible:N
W2S DEBUG: Foot(142.0,65.1,4.5)->Screen(2893.2,826.2) Visible:N
W2S DEBUG: Head(142.5,65.1,4.5)->Screen(2899.8,826.2) Visible:N
W2S DEBUG: Foot(142.5,65.1,4.5)->Screen(2899.8,826.2) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.7,826.2) Visible:N
RECOIL DEBUG: ViewPunch values: 0.000, 0.000, 0.000
W2S DEBUG: Head(142.8,65.1,4.5)->Screen(2904.1,826.2) Visible:N
W2S DEBUG: Foot(142.8,65.1,4.5)->Screen(2904.1,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.4,65.1,4.5)->Screen(2898.8,826.2) Visible:N
W2S DEBUG: Foot(142.4,65.1,4.5)->Screen(2898.8,826.2) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(141.9,65.1,4.5)->Screen(2892.0,826.2) Visible:N
W2S DEBUG: Foot(141.9,65.1,4.5)->Screen(2892.0,826.2) Visible:N
W2S DEBUG: Head(141.3,65.1,4.5)->Screen(2885.1,826.2) Visible:N
W2S DEBUG: Foot(141.3,65.1,4.5)->Screen(2885.1,826.2) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(141.2,65.1,4.5)->Screen(2883.0,826.2) Visible:N
W2S DEBUG: Foot(141.2,65.1,4.5)->Screen(2883.0,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(142.0,65.1,4.5)->Screen(2893.8,826.2) Visible:N
W2S DEBUG: Foot(142.0,65.1,4.5)->Screen(2893.8,826.2) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.5,826.2) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Foot(142.9,65.1,4.5)->Screen(2905.6,826.2) Visible:N
W2S DEBUG: Head(142.8,65.1,4.5)->Screen(4230.7,942.4) Visible:N
W2S DEBUG: Foot(142.8,65.1,4.5)->Screen(4230.7,942.4) Visible:N
W2S DEBUG: Head(141.9,65.1,4.5)->Screen(3402.7,847.9) Visible:N
W2S DEBUG: Foot(141.9,65.1,4.5)->Screen(3402.7,847.9) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(141.4,65.1,5.1)->Screen(2061.4,736.7) Visible:Y
W2S DEBUG: Foot(141.4,65.1,5.1)->Screen(2061.4,736.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.781 0.046 0.624 0.624]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(141.1,65.1,7.6)->Screen(1569.1,708.8) Visible:Y
W2S DEBUG: Foot(141.1,65.1,7.6)->Screen(1569.1,708.8) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(140.1,66.1,7.1)->Screen(421.2,656.4) Visible:Y
W2S DEBUG: Foot(140.1,66.1,7.1)->Screen(421.2,656.4) Visible:Y
W2S DEBUG: Head(140.1,66.1,4.5)->Screen(936.9,699.7) Visible:Y
W2S DEBUG: Foot(140.1,66.1,4.5)->Screen(936.9,699.7) Visible:Y
W2S DEBUG: Head(137.6,67.1,4.5)->Screen(1301.4,694.6) Visible:Y
W2S DEBUG: Foot(137.6,67.1,4.5)->Screen(1301.4,694.6) Visible:Y
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(135.7,69.7,4.5)->Screen(1351.3,698.1) Visible:Y
W2S DEBUG: Foot(135.7,69.7,4.5)->Screen(1351.3,698.1) Visible:Y
W2S DEBUG: Head(134.3,72.6,4.5)->Screen(1698.5,662.4) Visible:Y
W2S DEBUG: Foot(134.3,72.6,4.5)->Screen(1698.5,662.4) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(133.0,75.7,4.5)->Screen(1717.0,664.3) Visible:Y
W2S DEBUG: Foot(133.0,75.7,4.5)->Screen(1717.0,664.3) Visible:Y
W2S DEBUG: Head(131.8,78.8,4.5)->Screen(1748.2,646.1) Visible:Y
W2S DEBUG: Foot(131.8,78.8,4.5)->Screen(1748.2,646.1) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.957 0.241 -0.256 -0.256]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(130.5,81.9,4.5)->Screen(5909.0,1723.1) Visible:N
W2S DEBUG: Foot(130.5,81.9,4.5)->Screen(5909.0,1723.1) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(129.3,84.1,9.2)->Screen(5639.6,1478.5) Visible:N
W2S DEBUG: Foot(129.3,84.1,9.2)->Screen(5639.6,1478.5) Visible:N
W2S DEBUG: Head(128.2,86.8,11.1)->Screen(3314.8,1175.5) Visible:N
W2S DEBUG: Foot(128.2,86.8,11.1)->Screen(3314.8,1175.5) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(128.1,86.9,10.5)->Screen(1892.9,799.8) Visible:Y
W2S DEBUG: Foot(128.1,86.9,10.5)->Screen(1892.9,799.8) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(131.3,87.4,10.1)->Screen(9827.0,11620.1) Visible:N
W2S DEBUG: Foot(131.3,87.4,10.1)->Screen(9827.0,11620.1) Visible:N
W2S DEBUG: Head(131.1,90.4,11.1)->Screen(7268.8,8822.3) Visible:N
W2S DEBUG: Foot(131.1,90.4,11.1)->Screen(7268.8,8822.3) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(129.5,91.1,14.1)->Screen(1573.4,1656.5) Visible:N
W2S DEBUG: Foot(129.5,91.1,14.1)->Screen(1573.4,1656.5) Visible:N
W2S DEBUG: Head(127.4,91.3,14.0)->Screen(1344.7,1099.7) Visible:Y
W2S DEBUG: Foot(127.4,91.3,14.0)->Screen(1344.7,1099.7) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.970 0.281 -0.184 -0.184]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(124.9,91.2,12.2)->Screen(5969.9,1837.4) Visible:N
W2S DEBUG: Foot(124.9,91.2,12.2)->Screen(5969.9,1837.4) Visible:N
W2S DEBUG: Head(122.4,91.6,7.4)->Screen(6289.4,2106.0) Visible:N
W2S DEBUG: Foot(122.4,91.6,7.4)->Screen(6289.4,2106.0) Visible:N
W2S DEBUG: Head(119.7,92.4,7.6)->Screen(5504.8,1820.3) Visible:N
W2S DEBUG: Foot(119.7,92.4,7.6)->Screen(5504.8,1820.3) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(116.9,93.9,8.4)->Screen(1776.9,524.9) Visible:Y
W2S DEBUG: Foot(116.9,93.9,8.4)->Screen(1776.9,524.9) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(114.2,95.5,6.3)->Screen(1231.4,481.5) Visible:Y
W2S DEBUG: Foot(114.2,95.5,6.3)->Screen(1231.4,481.5) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(111.1,95.9,5.5)->Screen(1297.0,565.5) Visible:Y
W2S DEBUG: Foot(111.1,95.9,5.5)->Screen(1297.0,565.5) Visible:Y
W2S DEBUG: Head(108.0,94.1,4.9)->Screen(6366.5,2411.1) Visible:N
W2S DEBUG: Foot(108.0,94.1,4.9)->Screen(6366.5,2411.1) Visible:N
W2S DEBUG: Head(104.7,92.5,4.5)->Screen(5912.1,2375.1) Visible:N
W2S DEBUG: Foot(104.7,92.5,4.5)->Screen(5912.1,2375.1) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.964 0.318 -0.198 -0.198]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(101.4,91.7,4.5)->Screen(5618.2,2361.8) Visible:N
W2S DEBUG: Foot(101.4,91.7,4.5)->Screen(5618.2,2361.8) Visible:N
W2S DEBUG: Head(98.4,91.4,4.5)->Screen(5374.3,2348.3) Visible:N
W2S DEBUG: Foot(98.4,91.4,4.5)->Screen(5374.3,2348.3) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(97.0,92.1,4.5)->Screen(5357.1,2353.9) Visible:N
W2S DEBUG: Foot(97.0,92.1,4.5)->Screen(5357.1,2353.9) Visible:N
W2S DEBUG: Head(95.0,93.2,4.5)->Screen(5313.7,2360.3) Visible:N
W2S DEBUG: Foot(95.0,93.2,4.5)->Screen(5313.7,2360.3) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(93.9,94.8,4.5)->Screen(5374.2,2376.0) Visible:N
W2S DEBUG: Foot(93.9,94.8,4.5)->Screen(5374.2,2376.0) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(93.6,96.5,4.8)->Screen(5490.0,2378.2) Visible:N
W2S DEBUG: Foot(93.6,96.5,4.8)->Screen(5490.0,2378.2) Visible:N
W2S DEBUG: Head(93.7,98.3,4.6)->Screen(5701.7,2412.9) Visible:N
W2S DEBUG: Foot(93.7,98.3,4.6)->Screen(5701.7,2412.9) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(94.0,100.0,4.6)->Screen(5929.3,2442.7) Visible:N
W2S DEBUG: Foot(94.0,100.0,4.6)->Screen(5929.3,2442.7) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.964 0.317 -0.196 -0.196]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(94.5,101.6,4.5)->Screen(6175.5,2491.8) Visible:N
W2S DEBUG: Foot(94.5,101.6,4.5)->Screen(6175.5,2491.8) Visible:N
W2S DEBUG: Head(95.3,103.0,4.5)->Screen(6465.3,2524.4) Visible:N
W2S DEBUG: Foot(95.3,103.0,4.5)->Screen(6465.3,2524.4) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(97.0,103.6,4.5)->Screen(6743.6,2547.7) Visible:N
W2S DEBUG: Foot(97.0,103.6,4.5)->Screen(6743.6,2547.7) Visible:N
W2S DEBUG: Head(98.7,104.1,4.5)->Screen(7021.7,2569.9) Visible:N
W2S DEBUG: Foot(98.7,104.1,4.5)->Screen(7021.7,2569.9) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(100.3,104.3,4.5)->Screen(7258.1,2586.6) Visible:N
W2S DEBUG: Foot(100.3,104.3,4.5)->Screen(7258.1,2586.6) Visible:N
W2S DEBUG: Head(102.0,104.3,4.5)->Screen(7455.0,2597.2) Visible:N
W2S DEBUG: Foot(102.0,104.3,4.5)->Screen(7455.0,2597.2) Visible:N
W2S DEBUG: Head(103.6,104.1,4.5)->Screen(7622.5,2603.9) Visible:N
W2S DEBUG: Foot(103.6,104.1,4.5)->Screen(7622.5,2603.9) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(105.3,103.8,4.5)->Screen(7778.2,2608.9) Visible:N
W2S DEBUG: Foot(105.3,103.8,4.5)->Screen(7778.2,2608.9) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.964 0.317 -0.196 -0.196]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(106.4,103.6,4.5)->Screen(7876.6,2611.8) Visible:N
W2S DEBUG: Foot(106.4,103.6,4.5)->Screen(7876.6,2611.8) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(106.4,103.6,4.5)->Screen(7876.6,2611.8) Visible:N
W2S DEBUG: Foot(106.4,103.6,4.5)->Screen(7876.6,2611.8) Visible:N
W2S DEBUG: Head(106.4,103.6,4.5)->Screen(7876.6,2611.8) Visible:N
W2S DEBUG: Foot(106.4,103.6,4.5)->Screen(7876.6,2611.8) Visible:N
W2S DEBUG: Head(106.6,102.9,4.8)->Screen(7671.4,2560.7) Visible:N
W2S DEBUG: Foot(106.6,102.9,4.8)->Screen(7671.4,2560.7) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(106.2,101.6,5.5)->Screen(7231.0,2456.8) Visible:N
W2S DEBUG: Foot(106.2,101.6,5.5)->Screen(7231.0,2456.8) Visible:N
W2S DEBUG: Head(104.9,100.9,5.5)->Screen(6975.4,2440.3) Visible:N
W2S DEBUG: Foot(104.9,100.9,5.5)->Screen(6975.4,2440.3) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(103.2,100.6,4.5)->Screen(6910.6,2519.7) Visible:N
W2S DEBUG: Foot(103.2,100.6,4.5)->Screen(6910.6,2519.7) Visible:N
W2S DEBUG: Head(101.8,100.0,4.5)->Screen(6663.6,2499.8) Visible:N
W2S DEBUG: Foot(101.8,100.0,4.5)->Screen(6663.6,2499.8) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.964 0.317 -0.196 -0.196]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(100.2,99.3,4.5)->Screen(6400.3,2478.1) Visible:N
W2S DEBUG: Foot(100.2,99.3,4.5)->Screen(6400.3,2478.1) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
W2S DEBUG: Head(98.7,98.9,4.5)->Screen(6196.6,2462.8) Visible:N
W2S DEBUG: Foot(98.7,98.9,4.5)->Screen(6196.6,2462.8) Visible:N
W2S DEBUG: Head(97.0,98.7,4.5)->Screen(6021.3,2452.1) Visible:N
W2S DEBUG: Foot(97.0,98.7,4.5)->Screen(6021.3,2452.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(95.4,98.6,4.5)->Screen(5876.8,2444.2) Visible:N
W2S DEBUG: Foot(95.4,98.6,4.5)->Screen(5876.8,2444.2) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x10A01B78
Entity[3]: 0x10AC6818
Entity[4]: 0x11C92170
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 20.45, 161.53, 12.50
W2S DEBUG: Head(93.7,98.5,4.6)->Screen(5705.5,2424.4) Visible:N
W2S DEBUG: Foot(93.7,98.5,4.6)->Screen(5705.5,2424.4) Visible:N
W2S DEBUG: Head(92.1,98.5,4.6)->Screen(5568.9,2417.6) Visible:N
W2S DEBUG: Foot(92.1,98.5,4.6)->Screen(5568.9,2417.6) Visible:N
ENTITY DEBUG [4]: 0x11C92170 - H:100 T:1 A:Y
