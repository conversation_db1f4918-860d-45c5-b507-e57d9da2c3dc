AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window!
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [4]: 0x10B87878 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x10AC6818
Entity[2]: 0x11C92170
Entity[3]: 0x01210CC0
Entity[4]: 0x10B87878
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 98.21, 71.99, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
