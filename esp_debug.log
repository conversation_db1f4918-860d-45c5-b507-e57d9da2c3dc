AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window! Size: 2560x1440
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.117 0.000 0.986 0.986]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 29.10, 51.28, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(93.5,81.8,9.5)->Screen(1534.2,601.9) Visible:Y
W2S DEBUG: Foot(93.5,81.8,9.5)->Screen(1534.2,601.9) Visible:Y
W2S DEBUG: Head(91.0,83.7,9.5)->Screen(1573.1,598.1) Visible:Y
W2S DEBUG: Foot(91.0,83.7,9.5)->Screen(1573.1,598.1) Visible:Y
W2S DEBUG: Head(88.1,84.9,9.5)->Screen(1609.6,593.0) Visible:Y
W2S DEBUG: Foot(88.1,84.9,9.5)->Screen(1609.6,593.0) Visible:Y
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(85.0,84.7,9.5)->Screen(1630.8,586.4) Visible:Y
W2S DEBUG: Foot(85.0,84.7,9.5)->Screen(1630.8,586.4) Visible:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 29.10, 51.28, 0.50
W2S DEBUG: Head(83.3,83.7,9.5)->Screen(1631.7,582.3) Visible:Y
W2S DEBUG: Foot(83.3,83.7,9.5)->Screen(1631.7,582.3) Visible:Y
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(84.5,82.1,9.5)->Screen(1598.5,584.4) Visible:Y
W2S DEBUG: Foot(84.5,82.1,9.5)->Screen(1598.5,584.4) Visible:Y
W2S DEBUG: Head(87.3,82.5,9.5)->Screen(1583.5,590.5) Visible:Y
W2S DEBUG: Foot(87.3,82.5,9.5)->Screen(1583.5,590.5) Visible:Y
W2S DEBUG: Head(89.5,84.6,9.5)->Screen(1594.8,595.6) Visible:Y
W2S DEBUG: Foot(89.5,84.6,9.5)->Screen(1594.8,595.6) Visible:Y
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.951 -0.061 -0.309 -0.309]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(91.0,86.1,9.5)->Screen(-1315.1,15.4) Visible:N
W2S DEBUG: Foot(91.0,86.1,9.5)->Screen(-1315.1,15.4) Visible:N
W2S DEBUG: Head(93.4,86.7,9.5)->Screen(-1425.6,-22.3) Visible:N
W2S DEBUG: Foot(93.4,86.7,9.5)->Screen(-1425.6,-22.3) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(94.7,85.1,9.5)->Screen(-2249.1,-88.2) Visible:N
W2S DEBUG: Foot(94.7,85.1,9.5)->Screen(-2249.1,-88.2) Visible:N
W2S DEBUG: Head(94.5,82.6,9.5)->Screen(86805.3,16906.4) Visible:N
W2S DEBUG: Foot(94.5,82.6,9.5)->Screen(86805.3,16906.4) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(95.9,80.8,9.5)->Screen(10076.4,2409.6) Visible:N
W2S DEBUG: Foot(95.9,80.8,9.5)->Screen(10076.4,2409.6) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(98.2,80.6,9.5)->Screen(6526.3,1712.5) Visible:N
W2S DEBUG: Foot(98.2,80.6,9.5)->Screen(6526.3,1712.5) Visible:N
W2S DEBUG: Head(99.9,80.9,9.5)->Screen(6122.4,1591.8) Visible:N
W2S DEBUG: Foot(99.9,80.9,9.5)->Screen(6122.4,1591.8) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(101.9,81.6,9.5)->Screen(5950.5,1529.0) Visible:N
W2S DEBUG: Foot(101.9,81.6,9.5)->Screen(5950.5,1529.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.657 -0.011 -0.754 -0.754]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(104.7,82.0,9.5)->Screen(5581.0,1435.7) Visible:N
W2S DEBUG: Foot(104.7,82.0,9.5)->Screen(5581.0,1435.7) Visible:N
W2S DEBUG: Head(106.5,81.3,9.5)->Screen(5180.7,1360.8) Visible:N
W2S DEBUG: Foot(106.5,81.3,9.5)->Screen(5180.7,1360.8) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(106.6,78.8,9.5)->Screen(4713.2,1302.8) Visible:N
W2S DEBUG: Foot(106.6,78.8,9.5)->Screen(4713.2,1302.8) Visible:N
W2S DEBUG: Head(107.9,77.1,9.5)->Screen(4358.2,1245.5) Visible:N
W2S DEBUG: Foot(107.9,77.1,9.5)->Screen(4358.2,1245.5) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(110.2,79.2,9.5)->Screen(4498.2,1238.3) Visible:N
W2S DEBUG: Foot(110.2,79.2,9.5)->Screen(4498.2,1238.3) Visible:N
W2S DEBUG: Head(112.9,80.4,9.5)->Screen(4467.5,1208.7) Visible:N
W2S DEBUG: Foot(112.9,80.4,9.5)->Screen(4467.5,1208.7) Visible:N
W2S DEBUG: Head(115.3,78.9,9.5)->Screen(4140.0,1154.2) Visible:N
W2S DEBUG: Foot(115.3,78.9,9.5)->Screen(4140.0,1154.2) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(116.4,75.9,9.5)->Screen(3798.2,1111.7) Visible:N
W2S DEBUG: Foot(116.4,75.9,9.5)->Screen(3798.2,1111.7) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.657 -0.011 -0.754 -0.754]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(115.7,74.0,9.5)->Screen(3655.1,1102.3) Visible:N
W2S DEBUG: Foot(115.7,74.0,9.5)->Screen(3655.1,1102.3) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(114.8,72.8,9.5)->Screen(3580.5,1100.4) Visible:N
W2S DEBUG: Foot(114.8,72.8,9.5)->Screen(3580.5,1100.4) Visible:N
W2S DEBUG: Head(113.2,71.4,9.5)->Screen(3506.8,1103.8) Visible:N
W2S DEBUG: Foot(113.2,71.4,9.5)->Screen(3506.8,1103.8) Visible:N
W2S DEBUG: Head(112.1,72.1,9.5)->Screen(3601.4,1122.0) Visible:N
W2S DEBUG: Foot(112.1,72.1,9.5)->Screen(3601.4,1122.0) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(112.1,72.8,11.9)->Screen(3668.0,1242.0) Visible:N
W2S DEBUG: Foot(112.1,72.8,11.9)->Screen(3668.0,1242.0) Visible:N
W2S DEBUG: Head(112.8,72.9,13.2)->Screen(3648.9,1290.7) Visible:N
W2S DEBUG: Foot(112.8,72.9,13.2)->Screen(3648.9,1290.7) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(114.5,73.2,11.0)->Screen(3621.9,1171.2) Visible:N
W2S DEBUG: Foot(114.5,73.2,11.0)->Screen(3621.9,1171.2) Visible:N
W2S DEBUG: Head(116.3,74.6,11.4)->Screen(3685.2,1182.3) Visible:N
W2S DEBUG: Foot(116.3,74.6,11.4)->Screen(3685.2,1182.3) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.657 -0.011 -0.754 -0.754]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(117.7,75.4,10.1)->Screen(3707.8,1119.8) Visible:N
W2S DEBUG: Foot(117.7,75.4,10.1)->Screen(3707.8,1119.8) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(117.4,74.3,9.5)->Screen(3623.4,1087.6) Visible:N
W2S DEBUG: Foot(117.4,74.3,9.5)->Screen(3623.4,1087.6) Visible:N
W2S DEBUG: Head(115.7,72.0,9.5)->Screen(3484.8,1084.5) Visible:N
W2S DEBUG: Foot(115.7,72.0,9.5)->Screen(3484.8,1084.5) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(114.2,70.0,9.5)->Screen(3371.4,1082.6) Visible:N
W2S DEBUG: Foot(114.2,70.0,9.5)->Screen(3371.4,1082.6) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.657 -0.011 -0.754 -0.754]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.657 -0.011 -0.754 -0.754]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.657 -0.011 -0.754 -0.754]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.657 -0.011 -0.754 -0.754]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.657 -0.011 -0.754 -0.754]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x11D48EC0
Entity[2]: 0x11D4B2F8
Entity[3]: 0x10A01B78
Entity[4]: 0x10AC6818
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.657 -0.011 -0.754 -0.754]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 58.00, 52.00, 0.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ENTITY DEBUG [4]: 0x10AC6818 - H:100 T:1 A:Y
W2S DEBUG: Head(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
W2S DEBUG: Foot(113.9,68.0,9.5)->Screen(3230.1,1070.1) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=1
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Players: 1, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 1 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=1
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 -0.017 -0.017]
Players: 1, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 1 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=1
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Players: 1, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 1 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=1
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 -0.017 -0.017]
Players: 1, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 1 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=1
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=1, LocalPlayer=0x02D2A158
Players: 1, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 1 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=1
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 -0.017 -0.017]
Players: 1, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 1 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=1
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Players: 1, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 1 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=1
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 -0.017 -0.017]
Players: 1, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 1 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=1
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Players: 1, LocalPlayer: 0x02D2A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 1 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
W2S DEBUG: Head(182.9,106.0,12.5)->Screen(3455.1,720.0) Visible:N
W2S DEBUG: Foot(182.9,106.0,12.5)->Screen(3455.1,720.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x02D2A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x10ABBE40
Entity[0]: 0x00000000
Entity[1]: 0x109F5D50
Entity[2]: 0x11D4B2F8
Entity[3]: 0x11D48EC0
Entity[4]: 0x10A01B78
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02D2A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [1.000 0.000 -0.017 -0.017]
Players: 8, LocalPlayer: 0x02D2A158
Entities processed: 4
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x00000000
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x02D2A158
PlayerCount 0x50F500: 0 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00000000
EntityList 0x10F4F8: 0x10ABBE40
EntityList 0x18AC04: 0x00000000
Local pos: 87.00, 165.00, 12.50
W2S DEBUG: Head(179.8,105.8,12.5)->Screen(3374.1,720.0) Visible:N
W2S DEBUG: Foot(179.8,105.8,12.5)->Screen(3374.1,720.0) Visible:N
