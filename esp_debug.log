AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window! Size: 2560x1440
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
VIEWMATRIX DEBUG: Valid=Y [0.125 0.259 0.982 0.981]
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.125 0.259 0.982 0.981]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 100.00, 114.00, 11.50
ENTITY COUNT DEBUG: Found 7 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 108.82, 114.86, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.675 0.619 0.651 0.650]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 110.73, 113.99, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 105.51, 112.87, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
VIEWMATRIX DEBUG: Valid=Y [-0.825 0.224 0.551 0.550]
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ENTITY COUNT DEBUG: Found 7 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ENTITY COUNT DEBUG: Found 7 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
VIEWMATRIX DEBUG: Valid=Y [-0.825 0.224 0.551 0.550]
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
VIEWMATRIX DEBUG: Valid=Y [-0.825 0.224 0.551 0.550]
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ENTITY COUNT DEBUG: Found 7 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
VIEWMATRIX DEBUG: Valid=Y [-0.825 0.224 0.551 0.550]
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ENTITY COUNT DEBUG: Found 7 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ENTITY COUNT DEBUG: Found 7 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 7
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
VIEWMATRIX DEBUG: Valid=Y [-0.825 0.224 0.551 0.550]
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.825 0.224 0.551 0.550]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.53, 114.62, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 106.37, 114.38, 10.50
ENTITY COUNT DEBUG: Found 6 valid entities out of 32 slots
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.220 0.524 0.930 0.930]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 6
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 112.10, 115.46, 10.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 126.91, 112.48, 3.54
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.778 0.119 0.625 0.624]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 136.69, 120.50, 3.50
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 147.61, 128.60, 3.19
ESP Check: enabled=YES, localPlayer=0x02C8A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FDFAB18
Entity[0]: 0x00000000
Entity[1]: 0x0FE7E010
Entity[2]: 0x0FCB3550
Entity[3]: 0x0FE96020
Entity[4]: 0x0FEAF7D8
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x02C8A158
VIEWMATRIX DEBUG: Valid=Y [0.790 -0.025 -0.613 -0.613]
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.790 -0.025 -0.613 -0.613]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x02C8A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x0001C838
LocalPlayer 0x50F4F4: 0x00200079
LocalPlayer 0x10F4F4: 0x02C8A158
PlayerCount 0x50F500: 7209071 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00490028
EntityList 0x10F4F8: 0x0FDFAB18
EntityList 0x18AC04: 0x00000000
Local pos: 154.47, 129.56, 1.50
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
