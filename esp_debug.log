AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window! Size: 2560x1440
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
W2S DEBUG: Head(140.2,36.8,3.8)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(140.2,36.8,3.8)->Screen(0.0,0.0) Visible:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY COUNT DEBUG: Found 4 valid entities out of 32 slots
W2S DEBUG: Head(139.6,35.1,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(139.6,35.1,4.5)->Screen(0.0,0.0) Visible:N
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
W2S DEBUG: Head(136.8,35.2,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(136.8,35.2,4.5)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Head(134.7,37.4,3.8)->Screen(0.0,0.0) Visible:N
W2S DEBUG: Foot(134.7,37.4,3.8)->Screen(0.0,0.0) Visible:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
PLAYER COUNT DEBUG: Read playerCount = 8
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=8, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY COUNT DEBUG: Found 5 valid entities out of 32 slots
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.463 0.000 0.751 0.751]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
TEAM DEBUG: Local team: 1, Entity team: 1, IsEnemy: N, ForceAll: N
ENTITY DEBUG [0]: 0x00000000 - NULL
ENTITY DEBUG [4]: 0x0FE37DB0 - H:100 T:0 A:N
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=8
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Entity[2]: 0x0FE5C358
Entity[3]: 0x0FC91AC0
Entity[4]: 0x0FE37DB0
Players: 8, LocalPlayer: 0x0121A158
Entities processed: 5
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 8 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 146.68, 38.84, 3.50
