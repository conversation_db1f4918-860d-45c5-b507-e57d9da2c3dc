AssaultCube External ESP Starting...
Waiting for Assault Cube...
Attached to Assault Cube!
Found AssaultCube window! Size: 2560x1440
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=0, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.841 -0.706 -0.367 -0.367]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 215.30, 46.60, 2.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 215.33, 46.56, 2.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.935 -0.089 -0.352 -0.352]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 215.33, 46.56, 2.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 215.33, 46.56, 2.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.036 -0.310 -0.985 -0.984]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 215.33, 46.56, 2.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=0, LocalPlayer=0x0121A158
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 215.33, 46.56, 2.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.976 -0.230 -0.175 -0.175]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 215.56, 45.64, 2.44
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 214.30, 33.07, 2.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.458 -0.532 -0.838 -0.837]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 202.07, 30.33, 2.88
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 194.17, 39.19, -2.10
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=0, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.222 -0.144 -0.972 -0.972]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 188.85, 53.79, -9.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 178.58, 56.23, -9.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.046 -0.198 -0.994 -0.993]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 165.21, 56.85, -9.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 151.91, 57.54, -10.07
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.595 -0.134 -0.800 -0.800]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 132.46, 58.98, -12.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=0, LocalPlayer=0x0121A158
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.92, 58.01, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.975 -0.040 -0.222 -0.222]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.975 -0.040 -0.222 -0.222]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=0, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.975 -0.040 -0.222 -0.222]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.486 0.434 0.840 0.839]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.486 0.434 0.840 0.839]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=0, LocalPlayer=0x0121A158
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.486 0.434 0.840 0.839]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.88, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 122.91, 58.00, -15.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.935 -0.054 -0.353 -0.353]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 136.65, 56.55, -12.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 134.07, 57.85, -12.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=0, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.397 0.040 0.918 0.918]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 134.07, 57.85, -12.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 134.07, 57.85, -12.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [-0.397 0.040 0.918 0.918]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 0, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 0 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x00000000
EntityList 0x18AC04: 0x00000000
Local pos: 134.07, 57.85, -12.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 169.00, 69.00, 3.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.698 -0.163 -0.710 -0.710]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.67, 70.54, 3.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=2, LocalPlayer=0x0121A158
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.465 -0.172 -0.880 -0.880]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.465 -0.172 -0.880 -0.880]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=2, LocalPlayer=0x0121A158
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.465 -0.172 -0.880 -0.880]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.465 -0.172 -0.880 -0.880]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.465 -0.172 -0.880 -0.880]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
MAIN LOOP USING: PlayerCount offset=0x10F500, LocalPlayer offset=0x10F4F4
MAIN LOOP READING: PlayerCount=2, LocalPlayer=0x0121A158
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ENTITY DEBUG [0]: 0x00000000 - NULL
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
VIEWMATRIX SCAN: Searching for valid matrix...
POTENTIAL MATRIX at 0x501AE8: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x10F580: [0.000 0.000 0.000 0.000]
POTENTIAL MATRIX at 0x101AE8: [0.465 -0.172 -0.880 -0.880]
POTENTIAL MATRIX at 0x50F580: [0.000 0.000 0.000 0.000]
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
ESP Check: enabled=YES, localPlayer=0x0121A158, playerCount=2
EntityList offset: 0x10F4F8, EntityList base: 0x0FD9A4A0
Entity[0]: 0x00000000
Entity[1]: 0x0FE52CA0
Players: 2, LocalPlayer: 0x0121A158
Entities processed: 0
ModuleBase: 0x00400000
=== OFFSET TESTING ===
LocalPlayer 0x509B74: 0x000026FA
LocalPlayer 0x50F4F4: 0x00000000
LocalPlayer 0x10F4F4: 0x0121A158
PlayerCount 0x50F500: 65537 (should be 8)
PlayerCount 0x10F500: 2 (should be 8)
PlayerCount 0x18AC0C: 0 (should be 8)
EntityList 0x50F4F8: 0x00017429
EntityList 0x10F4F8: 0x0FD9A4A0
EntityList 0x18AC04: 0x00000000
Local pos: 173.75, 70.56, 3.50
