#pragma once

#define _USE_MATH_DEFINES
#include <cmath>
#include <algorithm>
#include <Windows.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// Helper clamp function for older C++ standards
template<typename T>
constexpr const T& clamp(const T& v, const T& lo, const T& hi) {
    return (v < lo) ? lo : (hi < v) ? hi : v;
}

struct vec {
    union {
        struct { float x, y, z; };
        float v[3];
        int i[3];
    };

    vec() { x = y = z = 0; }
    vec(float a, float b, float c) : x(a), y(b), z(c) {}
    vec(float* v) : x(v[0]), y(v[1]), z(v[2]) {}

    // Vector operations
    vec operator+(const vec& other) const { return vec(x + other.x, y + other.y, z + other.z); }
    vec operator-(const vec& other) const { return vec(x - other.x, y - other.y, z - other.z); }
    vec operator*(float scalar) const { return vec(x * scalar, y * scalar, z * scalar); }
    vec operator/(float scalar) const { return vec(x / scalar, y / scalar, z / scalar); }

    vec& operator+=(const vec& other) { x += other.x; y += other.y; z += other.z; return *this; }
    vec& operator-=(const vec& other) { x -= other.x; y -= other.y; z -= other.z; return *this; }
    vec& operator*=(float scalar) { x *= scalar; y *= scalar; z *= scalar; return *this; }
    vec& operator/=(float scalar) { x /= scalar; y /= scalar; z /= scalar; return *this; }

    // Vector math
    float dot(const vec& other) const { return x * other.x + y * other.y + z * other.z; }
    vec cross(const vec& other) const {
        return vec(
            y * other.z - z * other.y,
            z * other.x - x * other.z,
            x * other.y - y * other.x
        );
    }

    float length() const { return sqrtf(x * x + y * y + z * z); }
    float lengthSquared() const { return x * x + y * y + z * z; }

    vec& normalize() {
        float len = length();
        if (len != 0) {
            x /= len;
            y /= len;
            z /= len;
        }
        return *this;
    }

    vec normalized() const {
        float len = length();
        if (len != 0)
            return vec(x / len, y / len, z / len);
        return *this;
    }
};

struct glmatrixf {
    float v[16];

    float operator[](int i) const { return v[i]; }
    float& operator[](int i) { return v[i]; }

    void transform(const vec& in, vec& out) const {
        out.x = in.x * v[0] + in.y * v[4] + in.z * v[8] + v[12];
        out.y = in.x * v[1] + in.y * v[5] + in.z * v[9] + v[13];
        out.z = in.x * v[2] + in.y * v[6] + in.z * v[10] + v[14];
        float w = in.x * v[3] + in.y * v[7] + in.z * v[11] + v[15];

        if (w != 0.0f) {
            out.x /= w;
            out.y /= w;
            out.z /= w;
        }
    }
};

// Conversion operator implementation for Vec3
inline Vec3::operator vec() const {
    return vec(x, y, z);
}

namespace Math {
    // World to screen projection
    inline bool WorldToScreen(const Vec3& pos, vec& screen, const glmatrixf& matrix, int windowWidth, int windowHeight) {
        vec view;
        matrix.transform(static_cast<vec>(pos), view);

        if (view.z < 0.1f)
            return false;

        // Perspective division
        float invZ = 1.0f / view.z;
        screen.x = (windowWidth / 2.0f) + (view.x * invZ * windowWidth / 2.0f);
        screen.y = (windowHeight / 2.0f) - (view.y * invZ * windowHeight / 2.0f);
        screen.z = 0.0f;

        return screen.x >= 0 && screen.x < windowWidth &&
               screen.y >= 0 && screen.y < windowHeight;
    }

    // Distance calculations
    inline float Distance(const vec& a, const vec& b) {
        return (b - a).length();
    }

    inline float DistanceSquared(const vec& a, const vec& b) {
        return (b - a).lengthSquared();
    }

    // Angle calculations
    inline float RadiansToDegrees(float radians) {
        return radians * (180.0f / static_cast<float>(M_PI));
    }

    inline float DegreesToRadians(float degrees) {
        return degrees * (static_cast<float>(M_PI) / 180.0f);
    }

    // Calculate horizontal angle (yaw) between two points
    inline float CalcAngle(const vec& src, const vec& dst) {
        vec delta = dst - src;
        return std::atan2(delta.y, delta.x);
    }

    // Calculate vertical angle (pitch) between two points
    inline float CalcPitch(const vec& src, const vec& dst) {
        vec delta = dst - src;
        float dist = std::sqrt(delta.x * delta.x + delta.y * delta.y);
        return std::atan2(-delta.z, dist);
    }

    // Calculate both yaw and pitch angles between two points
    inline Vec2 CalcAngles(const vec& src, const vec& dst) {
        return Vec2(
            RadiansToDegrees(CalcPitch(src, dst)),
            RadiansToDegrees(CalcAngle(src, dst))
        );
    }

    // Normalize angle to -180 to 180 degrees
    inline float NormalizeAngle(float angle) {
        while (angle > 180.0f)
            angle -= 360.0f;
        while (angle < -180.0f)
            angle += 360.0f;
        return angle;
    }

    // Calculate angle difference (shortest path)
    inline float AngleDifference(float a, float b) {
        float diff = NormalizeAngle(a - b);
        if (diff > 180.0f)
            diff -= 360.0f;
        else if (diff < -180.0f)
            diff += 360.0f;
        return diff;
    }

    // Clamp angle between min and max
    inline float ClampAngle(float angle, float min, float max) {
        if (angle < -360.0f) angle += 360.0f;
        if (angle > 360.0f) angle -= 360.0f;
        return clamp(angle, min, max);
    }

    // Linear interpolation between angles
    inline float LerpAngle(float a, float b, float t) {
        float diff = AngleDifference(b, a);
        return NormalizeAngle(a + diff * clamp(t, 0.0f, 1.0f));
    }

    // Check if an angle is within a field of view
    inline bool IsAngleInFOV(float angle, float fov) {
        return std::abs(NormalizeAngle(angle)) <= fov * 0.5f;
    }
}
