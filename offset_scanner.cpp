#include <Windows.h>
#include <iostream>
#include <iomanip>
#include <vector>
#include "memory.h"

// Test different known offsets for AssaultCube
struct OffsetTest {
    const char* name;
    uintptr_t offset;
};

// Known offsets from various sources
OffsetTest localPlayerOffsets[] = {
    {"Guided Hacking Academy", 0x509B74},
    {"Common AC 1.2", 0x50F4F4},
    {"Alternative 1", 0x10F4F4},
    {"Alternative 2", 0x17E0A8}
};

OffsetTest entityListOffsets[] = {
    {"Standard", 0x50F4F8},
    {"Alternative 1", 0x10F4F8},
    {"Alternative 2", 0x18AC04}
};

OffsetTest playerCountOffsets[] = {
    {"Standard", 0x50F500},
    {"Alternative 1", 0x10F500},
    {"Alternative 2", 0x18AC0C}
};

void TestOffsets(Memory& mem, uintptr_t moduleBase) {
    std::cout << "\n=== OFFSET VERIFICATION TOOL ===" << std::endl;
    std::cout << "Module Base: 0x" << std::hex << moduleBase << std::dec << std::endl;
    
    std::cout << "\n--- Testing LocalPlayer Offsets ---" << std::endl;
    for (auto& test : localPlayerOffsets) {
        uintptr_t addr = moduleBase + test.offset;
        uintptr_t value = mem.Read<uintptr_t>(addr);
        std::cout << test.name << " (0x" << std::hex << test.offset << "): ";
        std::cout << "0x" << std::hex << value << std::dec;
        
        // Check if it looks like a valid pointer
        if (value > 0x400000 && value < 0x7FFFFFFF) {
            std::cout << " [VALID POINTER]";
            
            // Try to read position data
            float x = mem.Read<float>(value + 0x4);
            float y = mem.Read<float>(value + 0x8);
            float z = mem.Read<float>(value + 0xC);
            
            if (x != 0.0f || y != 0.0f || z != 0.0f) {
                std::cout << " Position: (" << x << ", " << y << ", " << z << ")";
            }
        } else if (value == 0) {
            std::cout << " [NULL]";
        } else {
            std::cout << " [INVALID]";
        }
        std::cout << std::endl;
    }
    
    std::cout << "\n--- Testing EntityList Offsets ---" << std::endl;
    for (auto& test : entityListOffsets) {
        uintptr_t addr = moduleBase + test.offset;
        uintptr_t value = mem.Read<uintptr_t>(addr);
        std::cout << test.name << " (0x" << std::hex << test.offset << "): ";
        std::cout << "0x" << std::hex << value << std::dec;
        
        if (value > 0x400000 && value < 0x7FFFFFFF) {
            std::cout << " [VALID POINTER]";
        } else if (value == 0) {
            std::cout << " [NULL]";
        } else {
            std::cout << " [INVALID]";
        }
        std::cout << std::endl;
    }
    
    std::cout << "\n--- Testing PlayerCount Offsets ---" << std::endl;
    for (auto& test : playerCountOffsets) {
        uintptr_t addr = moduleBase + test.offset;
        int value = mem.Read<int>(addr);
        std::cout << test.name << " (0x" << std::hex << test.offset << "): ";
        std::cout << std::dec << value;
        
        if (value >= 0 && value <= 32) {
            std::cout << " [REASONABLE]";
        } else {
            std::cout << " [OUT OF RANGE]";
        }
        std::cout << std::endl;
    }
}

int main() {
    std::cout << "AssaultCube Offset Scanner" << std::endl;
    std::cout << "=========================" << std::endl;
    
    Memory mem;
    
    std::cout << "Waiting for AssaultCube..." << std::endl;
    while (!mem.Attach(L"ac_client.exe", L"ac_client.exe")) {
        Sleep(1000);
    }
    
    std::cout << "Attached to AssaultCube!" << std::endl;
    uintptr_t moduleBase = mem.GetModuleBase();
    
    // Run tests every 3 seconds
    while (true) {
        TestOffsets(mem, moduleBase);
        
        std::cout << "\nPress Ctrl+C to exit, or waiting 3 seconds for next scan..." << std::endl;
        Sleep(3000);
        system("cls"); // Clear screen on Windows
    }
    
    return 0;
}
