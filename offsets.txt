AssaultCube ******* Memory Offsets
====================================

Game Module Base Name: "ac_client.exe"

Static Offsets (from ac_client.exe base address):
-------------------------------------------------
LocalPlayerPtrOffset = 0x50F4F4 (Type: DWORD or uintptr_t, points to LocalPlayer struct)
EntityListPtrOffset = 0x50F4F8 (Type: DWORD or uintptr_t, points to an array of Entity pointers)
PlayerCountOffset = 0x50F500 (Type: int, number of active players)
ViewMatrixOffset = 0x501AE8 (Type: float[16], 4x4 matrix)

Player/Entity Structure Offsets (relative to the base address of a specific player/entity struct):
-------------------------------------------------------------------------------------------------

Position & Angles:
X_Pos = 0x34 (Type: float) - Foot position X
Y_Pos = 0x38 (Type: float) - Foot position Y
Z_Pos = 0x3C (Type: float) - Foot position Z
Head_X = 0x04 (Type: float) - Head position X
Head_Y = 0x08 (Type: float) - Head position Y
Head_Z = 0x0C (Type: float) - Head position Z
Yaw = 0x40 (Type: float)
Pitch = 0x44 (Type: float)
Roll = 0x48 (Type: float)

Player Info:
Health = 0xF8 (Type: int)
Armor = 0xFC (Type: int)
Team = 0x32C (Type: int; common values: 1=Alpha, 2=Bravo, 0=Spectator/Neutral)
Name = 0x225 (Type: char[16])
IsDead = 0x32A (Type: int or byte; 0=Alive, 1=Dead)
MouseDown = 0x224 (Type: bool; mouse button state)

Weapon Ammo:
Pistol_Ammo = 0x12C (Type: int)
Shotgun_Ammo = 0x134 (Type: int)
MachineGun_Ammo = 0x138 (Type: int) - SMG
Sniper_Ammo = 0x13C (Type: int)
AssaultRifle_Ammo = 0x140 (Type: int)
Grenade_Ammo = 0x144 (Type: int)
Akimbo_Ammo = 0x148 (Type: int)
Carbine_Ammo = 0x150 (Type: int)

Weapon Clips:
Pistol_Clip = 0x16C (Type: int)
Shotgun_Clip = 0x174 (Type: int)
MachineGun_Clip = 0x178 (Type: int) - SMG
Sniper_Clip = 0x17C (Type: int)
AssaultRifle_Clip = 0x180 (Type: int)
Akimbo_Clip = 0x188 (Type: int)
Carbine_Clip = 0x190 (Type: int)

Weapon Timers:
Knife_Timer = 0x1B0 (Type: float)
Pistol_Timer = 0x1B4 (Type: float)
Shotgun_Timer = 0x1BC (Type: float)
MachineGun_Timer = 0x1C0 (Type: float) - SMG
Sniper_Timer = 0x1C4 (Type: float)
AssaultRifle_Timer = 0x1C8 (Type: float)
Grenade_Timer = 0x1CC (Type: float)
Carbine_Timer = 0x1D8 (Type: float)

Notes:
- These offsets are specifically for AssaultCube version *******
- All offsets are in hexadecimal format
- Entity pointers are accessed via: EntityList + (index * 0x4)
- Maximum players in a game: 32
- Virtual screen coordinates: 2400x1800 (for ESP calculations)