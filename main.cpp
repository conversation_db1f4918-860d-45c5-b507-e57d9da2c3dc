#include <Windows.h>
#include <d3d11.h>
#include <tchar.h>
#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include "imgui/imgui.h"
#include "imgui/imgui_impl_win32.h"
#include "imgui/imgui_impl_dx11.h"
#include "memory.h"
#include "entity.h"
#include "math.h"

// Forward declare the ImGui handler
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Link necessary libraries
#pragma comment(lib, "d3d11.lib")

// Globals
static ID3D11Device*           g_pd3dDevice = NULL;
static ID3D11DeviceContext*    g_pd3dDeviceContext = NULL;
static IDXGISwapChain*         g_pSwapChain = NULL;
static ID3D11RenderTargetView* g_mainRenderTargetView = NULL;

// Menu state
bool g_ShowMenu = false;
bool g_MenuKeyPressed = false;

// ESP Configuration
struct Config {
    bool enabled = true;
    bool showBoxes = true;
    bool showHealth = true;
    bool showName = true;
    bool showWeapons = false;
    bool showAmmo = false;
    bool showDistance = true;
    bool showTeam = true;
    bool showArmor = true;
    bool showState = false;
    bool showSnaplines = true;
    bool showHeadDot = true;
    
    // Visual settings
    float boxThickness = 2.0f;
    float snaplineThickness = 1.0f;
    ImVec4 enemyColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
    ImVec4 teamColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
    ImVec4 textColor = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    ImVec4 snaplineColor = ImVec4(1.0f, 1.0f, 0.0f, 0.8f);
    
    // Snapline position (0 = top, 1 = center, 2 = bottom)
    int snaplinePos = 2;
} config;

HWND hwnd = NULL;
Memory mem;
uintptr_t moduleBase = 0;
uintptr_t localPlayerAddr = 0;
int playerCount = 0;

glmatrixf viewMatrix;

bool CreateDeviceD3D(HWND hWnd);
void CleanupDeviceD3D();
void CreateRenderTarget();
void CleanupRenderTarget();
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
void UpdateWindowTransparency();

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE, LPSTR, int) {
    // Register window class
    WNDCLASSEX wc = { sizeof(WNDCLASSEX), CS_CLASSDC, WndProc, 0, 0,
                      GetModuleHandle(NULL), NULL, NULL, NULL, NULL,
                      _T("AssaultCubeESP"), NULL };
    RegisterClassEx(&wc);

    // Get primary monitor resolution
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);

    // Create fullscreen overlay window with proper extended styles
    hwnd = CreateWindowEx(
        WS_EX_TOPMOST | WS_EX_TRANSPARENT | WS_EX_LAYERED | WS_EX_TOOLWINDOW,
        wc.lpszClassName, 
        _T("Assault Cube External ESP"),
        WS_POPUP,
        0, 0, screenWidth, screenHeight,
        NULL, NULL, wc.hInstance, NULL);

    // Make window transparent
    SetLayeredWindowAttributes(hwnd, RGB(0, 0, 0), 0, LWA_COLORKEY);

    // Initialize Direct3D
    if (!CreateDeviceD3D(hwnd)) {
        CleanupDeviceD3D();
        UnregisterClass(wc.lpszClassName, wc.hInstance);
        return 1;
    }

    ShowWindow(hwnd, SW_SHOWDEFAULT);
    UpdateWindow(hwnd);

    // Setup ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup ImGui style
    ImGui::StyleColorsDark();
    ImGuiStyle& style = ImGui::GetStyle();
    style.WindowRounding = 5.0f;
    style.FrameRounding = 3.0f;
    style.Colors[ImGuiCol_WindowBg].w = 0.9f;

    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(hwnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

    // Attach to game process
    std::cout << "Waiting for Assault Cube..." << std::endl;
    while (!mem.Attach(L"ac_client.exe", L"ac_client.exe")) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }
    std::cout << "Attached to Assault Cube!" << std::endl;
    moduleBase = mem.GetModuleBase();

    // Set initial window state
    UpdateWindowTransparency();

    MSG msg;
    ZeroMemory(&msg, sizeof(msg));

    while (msg.message != WM_QUIT) {
        if (PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }

        // Handle menu toggle
        if (GetAsyncKeyState(VK_INSERT) & 1) {
            g_ShowMenu = !g_ShowMenu;
            UpdateWindowTransparency();
        }

        // Start ImGui frame
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // Read game data
        playerCount = mem.Read<int>(moduleBase + Offsets::PlayerCount);
        localPlayerAddr = mem.Read<uintptr_t>(moduleBase + Offsets::LocalPlayer);

        // Read view matrix
        for (int i = 0; i < 16; i++) {
            viewMatrix[i] = mem.Read<float>(moduleBase + Offsets::ViewMatrix + i * sizeof(float));
        }

        // Draw menu if visible
        if (g_ShowMenu) {
            ImGui::SetNextWindowPos(ImVec2(50, 50), ImGuiCond_FirstUseEver);
            ImGui::SetNextWindowSize(ImVec2(350, 500), ImGuiCond_FirstUseEver);
            
            ImGui::Begin("Assault Cube ESP", &g_ShowMenu, ImGuiWindowFlags_NoCollapse);
            
            ImGui::Text("Press INSERT to toggle menu");
            ImGui::Separator();
            
            ImGui::Checkbox("Enable ESP", &config.enabled);
            
            if (config.enabled) {
                ImGui::Separator();
                ImGui::Text("Visual Options");
                ImGui::Checkbox("Boxes", &config.showBoxes);
                ImGui::Checkbox("Health Bar", &config.showHealth);
                ImGui::Checkbox("Names", &config.showName);
                ImGui::Checkbox("Distance", &config.showDistance);
                ImGui::Checkbox("Team", &config.showTeam);
                ImGui::Checkbox("Armor", &config.showArmor);
                ImGui::Checkbox("Snaplines", &config.showSnaplines);
                ImGui::Checkbox("Head Dot", &config.showHeadDot);
                
                ImGui::Separator();
                ImGui::Text("Advanced Options");
                ImGui::Checkbox("Weapons", &config.showWeapons);
                ImGui::Checkbox("Ammo", &config.showAmmo);
                ImGui::Checkbox("State", &config.showState);
                
                ImGui::Separator();
                ImGui::Text("Visual Settings");
                ImGui::SliderFloat("Box Thickness", &config.boxThickness, 0.5f, 5.0f);
                ImGui::SliderFloat("Snapline Thickness", &config.snaplineThickness, 0.5f, 3.0f);
                
                const char* snaplinePositions[] = { "Top", "Center", "Bottom" };
                ImGui::Combo("Snapline Position", &config.snaplinePos, snaplinePositions, IM_ARRAYSIZE(snaplinePositions));
                
                ImGui::Separator();
                ImGui::Text("Colors");
                ImGui::ColorEdit4("Enemy Color", (float*)&config.enemyColor);
                ImGui::ColorEdit4("Team Color", (float*)&config.teamColor);
                ImGui::ColorEdit4("Text Color", (float*)&config.textColor);
                ImGui::ColorEdit4("Snapline Color", (float*)&config.snaplineColor);
            }
            
            ImGui::Separator();
            ImGui::Text("Players: %d", playerCount);
            
            ImGui::End();
        }

        // Draw ESP if enabled
        if (config.enabled && localPlayerAddr) {
            Entity localPlayer(localPlayerAddr, mem);
            Vec3 localPos = localPlayer.GetFootPosition();
            
            // Get draw list for ESP
            ImDrawList* drawList = ImGui::GetBackgroundDrawList();
            
            // Calculate snapline origin based on setting
            float snaplineY = 0;
            switch (config.snaplinePos) {
                case 0: snaplineY = 0; break; // Top
                case 1: snaplineY = screenHeight / 2.0f; break; // Center
                case 2: snaplineY = screenHeight - 1.0f; break; // Bottom
            }
            ImVec2 snaplineOrigin(screenWidth / 2.0f, snaplineY);
            
            // Loop through entities
            for (int i = 0; i < playerCount && i < 32; i++) {
                uintptr_t entityAddr = mem.Read<uintptr_t>(moduleBase + Offsets::EntityList + i * 0x4);
                if (!entityAddr || entityAddr == localPlayerAddr) continue;
                
                Entity entity(entityAddr, mem);
                if (!entity.IsAlive()) continue;
                
                // Get positions
                Vec3 headPos = entity.GetHeadPosition();
                Vec3 footPos = entity.GetFootPosition();
                
                // World to screen for head and foot
                vec screenHead, screenFoot;
                bool headVisible = Math::WorldToScreen(headPos, screenHead, viewMatrix, screenWidth, screenHeight);
                bool footVisible = Math::WorldToScreen(footPos, screenFoot, viewMatrix, screenWidth, screenHeight);
                
                if (!headVisible && !footVisible) continue;
                
                // Calculate distance
                float distance = Math::Distance(static_cast<vec>(localPos), static_cast<vec>(footPos));
                
                // Determine color based on team
                bool isEnemy = entity.GetTeam() != localPlayer.GetTeam();
                ImU32 boxColor = isEnemy ? 
                    ImGui::ColorConvertFloat4ToU32(config.enemyColor) : 
                    ImGui::ColorConvertFloat4ToU32(config.teamColor);
                ImU32 textColor = ImGui::ColorConvertFloat4ToU32(config.textColor);
                ImU32 snapColor = ImGui::ColorConvertFloat4ToU32(config.snaplineColor);
                
                // Calculate box dimensions
                float height = screenFoot.y - screenHead.y;
                float width = height * 0.4f;
                
                ImVec2 topLeft(screenHead.x - width / 2, screenHead.y);
                ImVec2 bottomRight(screenHead.x + width / 2, screenFoot.y);
                
                // Draw ESP box
                if (config.showBoxes) {
                    drawList->AddRect(topLeft, bottomRight, boxColor, 0.0f, 0, config.boxThickness);
                }
                
                // Draw health bar
                if (config.showHealth) {
                    int health = entity.GetHealth();
                    float healthPercent = health / 100.0f;
                    
                    float barWidth = 4.0f;
                    float barHeight = height;
                    ImVec2 healthBarTop(topLeft.x - 8.0f, topLeft.y);
                    ImVec2 healthBarBottom(healthBarTop.x + barWidth, topLeft.y + barHeight);
                    
                    // Background
                    drawList->AddRectFilled(healthBarTop, healthBarBottom, IM_COL32(0, 0, 0, 180));
                    
                    // Health bar
                    ImVec2 healthFillBottom(healthBarBottom.x, healthBarTop.y + (barHeight * healthPercent));
                    ImU32 healthColor = IM_COL32(
                        (int)(255 * (1.0f - healthPercent)),
                        (int)(255 * healthPercent),
                        0, 255
                    );
                    drawList->AddRectFilled(healthBarTop, healthFillBottom, healthColor);
                }
                
                // Draw armor bar
                if (config.showArmor) {
                    int armor = entity.GetArmor();
                    if (armor > 0) {
                        float armorPercent = armor / 100.0f;
                        
                        float barWidth = 4.0f;
                        float barHeight = height;
                        ImVec2 armorBarTop(bottomRight.x + 4.0f, topLeft.y);
                        ImVec2 armorBarBottom(armorBarTop.x + barWidth, topLeft.y + barHeight);
                        
                        // Background
                        drawList->AddRectFilled(armorBarTop, armorBarBottom, IM_COL32(0, 0, 0, 180));
                        
                        // Armor bar
                        ImVec2 armorFillBottom(armorBarBottom.x, armorBarTop.y + (barHeight * armorPercent));
                        drawList->AddRectFilled(armorBarTop, armorFillBottom, IM_COL32(66, 135, 245, 255));
                    }
                }
                
                // Draw snapline
                if (config.showSnaplines) {
                    drawList->AddLine(snaplineOrigin, ImVec2(screenHead.x, screenHead.y), snapColor, config.snaplineThickness);
                }
                
                // Draw head dot
                if (config.showHeadDot) {
                    drawList->AddCircleFilled(ImVec2(screenHead.x, screenHead.y), 3.0f, boxColor);
                }
                
                // Draw text info
                float textY = bottomRight.y + 2;
                
                if (config.showName) {
                    std::string name = entity.GetName();
                    drawList->AddText(ImVec2(screenHead.x - (name.length() * 3), textY), textColor, name.c_str());
                    textY += 15;
                }
                
                if (config.showDistance) {
                    char distStr[32];
                    sprintf_s(distStr, "%.1fm", distance);
                    drawList->AddText(ImVec2(screenHead.x - 15, textY), textColor, distStr);
                    textY += 15;
                }
                
                if (config.showTeam) {
                    char teamStr[32];
                    sprintf_s(teamStr, "Team %d", entity.GetTeam());
                    drawList->AddText(ImVec2(screenHead.x - 20, textY), textColor, teamStr);
                    textY += 15;
                }
                
                if (config.showWeapons || config.showAmmo) {
                    // Show current weapon info based on what has ammo
                    char weaponStr[128] = "";
                    
                    if (entity.GetCarbineClip() > 0 || entity.GetCarbineAmmo() > 0) {
                        sprintf_s(weaponStr, "Carbine: %d/%d", entity.GetCarbineClip(), entity.GetCarbineAmmo());
                    }
                    else if (entity.GetShotgunClip() > 0 || entity.GetShotgunAmmo() > 0) {
                        sprintf_s(weaponStr, "Shotgun: %d/%d", entity.GetShotgunClip(), entity.GetShotgunAmmo());
                    }
                    else if (entity.GetMachineGunClip() > 0 || entity.GetMachineGunAmmo() > 0) {
                        sprintf_s(weaponStr, "SMG: %d/%d", entity.GetMachineGunClip(), entity.GetMachineGunAmmo());
                    }
                    else if (entity.GetSniperClip() > 0 || entity.GetSniperAmmo() > 0) {
                        sprintf_s(weaponStr, "Sniper: %d/%d", entity.GetSniperClip(), entity.GetSniperAmmo());
                    }
                    else if (entity.GetAssaultRifleClip() > 0 || entity.GetAssaultRifleAmmo() > 0) {
                        sprintf_s(weaponStr, "AR: %d/%d", entity.GetAssaultRifleClip(), entity.GetAssaultRifleAmmo());
                    }
                    else {
                        sprintf_s(weaponStr, "Pistol: %d/%d", entity.GetPistolClip(), entity.GetPistolAmmo());
                    }
                    
                    if (strlen(weaponStr) > 0) {
                        drawList->AddText(ImVec2(screenHead.x - 30, textY), textColor, weaponStr);
                        textY += 15;
                    }
                }
                
                if (config.showState) {
                    if (entity.GetMouseDown()) {
                        drawList->AddText(ImVec2(screenHead.x - 20, textY), IM_COL32(255, 0, 0, 255), "FIRING");
                    }
                }
            }
        }

        // Rendering
        ImGui::Render();
        const float clear_color[4] = { 0.0f, 0.0f, 0.0f, 0.0f };
        g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, NULL);
        g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color);
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

        g_pSwapChain->Present(1, 0); // Present with vsync
    }

    // Cleanup
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    DestroyWindow(hwnd);
    UnregisterClass(wc.lpszClassName, wc.hInstance);

    return 0;
}

void UpdateWindowTransparency() {
    if (g_ShowMenu) {
        // When menu is shown, window should be interactable
        SetWindowLong(hwnd, GWL_EXSTYLE, WS_EX_TOPMOST | WS_EX_LAYERED | WS_EX_TOOLWINDOW);
        SetLayeredWindowAttributes(hwnd, 0, 255, LWA_ALPHA);
    }
    else {
        // When menu is hidden, window should be click-through
        SetWindowLong(hwnd, GWL_EXSTYLE, WS_EX_TOPMOST | WS_EX_TRANSPARENT | WS_EX_LAYERED | WS_EX_TOOLWINDOW);
        SetLayeredWindowAttributes(hwnd, RGB(0, 0, 0), 0, LWA_COLORKEY);
    }
}

bool CreateDeviceD3D(HWND hWnd) {
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;

    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = {
        D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0,
    };

    if (D3D11CreateDeviceAndSwapChain(NULL, D3D_DRIVER_TYPE_HARDWARE, NULL,
        createDeviceFlags, featureLevelArray, 2,
        D3D11_SDK_VERSION, &sd, &g_pSwapChain,
        &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext) != S_OK)
        return false;

    CreateRenderTarget();
    return true;
}

void CreateRenderTarget() {
    ID3D11Texture2D* pBackBuffer;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, NULL, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void CleanupRenderTarget() {
    if (g_mainRenderTargetView) {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = NULL;
    }
}

void CleanupDeviceD3D() {
    CleanupRenderTarget();
    if (g_pSwapChain) { g_pSwapChain->Release(); g_pSwapChain = NULL; }
    if (g_pd3dDeviceContext) { g_pd3dDeviceContext->Release(); g_pd3dDeviceContext = NULL; }
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = NULL; }
}

LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg) {
    case WM_SIZE:
        if (g_pd3dDevice != NULL && wParam != SIZE_MINIMIZED) {
            CleanupRenderTarget();
            g_pSwapChain->ResizeBuffers(0, (UINT)LOWORD(lParam), (UINT)HIWORD(lParam), DXGI_FORMAT_UNKNOWN, 0);
            CreateRenderTarget();
        }
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
            return 0;
        break;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}
