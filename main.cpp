#include <Windows.h>
#include <d3d11.h>
#include <tchar.h>
#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <float.h>
#include <cmath>
#include "imgui/imgui.h"
#include "imgui/imgui_impl_win32.h"
#include "imgui/imgui_impl_dx11.h"
#include "memory.h"
#include "entity.h"
#include "math.h"

// Forward declare the ImGui handler
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Link necessary libraries
#pragma comment(lib, "d3d11.lib")

// Globals
static ID3D11Device*           g_pd3dDevice = NULL;
static ID3D11DeviceContext*    g_pd3dDeviceContext = NULL;
static IDXGISwapChain*         g_pSwapChain = NULL;
static ID3D11RenderTargetView* g_mainRenderTargetView = NULL;

// Menu state
bool g_ShowMenu = false;
bool g_MenuKeyPressed = false;

// Game window tracking
HWND g_GameWindow = NULL;
FILE* g_DebugFile = nullptr;
int g_ScreenWidth = 1920;  // Default, will be updated
int g_ScreenHeight = 1080; // Default, will be updated

// ESP Configuration
struct Config {
    bool enabled = true;
    bool showBoxes = true;
    bool showHealth = true;
    bool showName = true;
    bool showWeapons = false;
    bool showStatus = false;  // Show "ESP Active" text
    bool showAmmo = false;
    bool showDistance = true;
    bool showTeam = true;
    bool showArmor = true;
    bool showState = false;
    bool showSnaplines = true;
    bool showHeadDot = true;

    // Cheat features
    bool noRecoil = false;
    bool aimbot = false;
    float aimbotFOV = 60.0f;
    float aimbotSmooth = 5.0f;
    int aimbotKey = VK_RBUTTON;  // Right mouse button

    // Visual settings
    float boxThickness = 2.0f;
    float snaplineThickness = 1.0f;
    ImVec4 enemyColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
    ImVec4 teamColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
    ImVec4 textColor = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    ImVec4 snaplineColor = ImVec4(1.0f, 1.0f, 0.0f, 0.8f);
    
    // Snapline position (0 = top, 1 = center, 2 = bottom)
    int snaplinePos = 2;
} config;

HWND hwnd = NULL;
Memory mem;
uintptr_t moduleBase = 0;
uintptr_t localPlayerAddr = 0;
int playerCount = 0;

glmatrixf viewMatrix;

bool CreateDeviceD3D(HWND hWnd);
void CleanupDeviceD3D();
void CreateRenderTarget();
void CleanupRenderTarget();
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
void UpdateWindowTransparency();
void UpdateOverlayPosition();

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE, LPSTR, int) {
    // Create debug log file
    fopen_s(&g_DebugFile, "esp_debug.log", "w");
    if (g_DebugFile) {
        fprintf(g_DebugFile, "AssaultCube External ESP Starting...\n");
        fflush(g_DebugFile);
    }

    // Register window class
    WNDCLASSEX wc = { sizeof(WNDCLASSEX), CS_CLASSDC, WndProc, 0, 0,
                      GetModuleHandle(NULL), NULL, NULL, NULL, NULL,
                      _T("AssaultCubeESP"), NULL };
    RegisterClassEx(&wc);

    // Initialize with desktop size, will be updated to game window size
    g_ScreenWidth = GetSystemMetrics(SM_CXSCREEN);
    g_ScreenHeight = GetSystemMetrics(SM_CYSCREEN);

    // Create fullscreen overlay window with proper extended styles
    hwnd = CreateWindowEx(
        WS_EX_TOPMOST | WS_EX_TRANSPARENT | WS_EX_LAYERED | WS_EX_TOOLWINDOW,
        wc.lpszClassName,
        _T("Assault Cube External ESP"),
        WS_POPUP,
        0, 0, g_ScreenWidth, g_ScreenHeight,
        NULL, NULL, wc.hInstance, NULL);

    // Make window transparent and click-through
    SetLayeredWindowAttributes(hwnd, RGB(0, 0, 0), 255, LWA_COLORKEY | LWA_ALPHA);

    // Initialize Direct3D
    if (!CreateDeviceD3D(hwnd)) {
        CleanupDeviceD3D();
        UnregisterClass(wc.lpszClassName, wc.hInstance);
        return 1;
    }

    ShowWindow(hwnd, SW_SHOWDEFAULT);
    UpdateWindow(hwnd);

    // Setup ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup ImGui style
    ImGui::StyleColorsDark();
    ImGuiStyle& style = ImGui::GetStyle();
    style.WindowRounding = 5.0f;
    style.FrameRounding = 3.0f;
    style.Colors[ImGuiCol_WindowBg].w = 0.9f;

    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(hwnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

    // Attach to game process
    if (g_DebugFile) {
        fprintf(g_DebugFile, "Waiting for Assault Cube...\n");
        fflush(g_DebugFile);
    }
    while (!mem.Attach(L"ac_client.exe", L"ac_client.exe")) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }
    if (g_DebugFile) {
        fprintf(g_DebugFile, "Attached to Assault Cube!\n");
        fflush(g_DebugFile);
    }
    moduleBase = mem.GetModuleBase();

    // Find the game window
    g_GameWindow = FindWindow(NULL, L"AssaultCube");
    if (!g_GameWindow) {
        g_GameWindow = FindWindow(NULL, L"Assault Cube");
    }
    if (g_GameWindow) {
        // Get actual AssaultCube window size
        RECT gameRect;
        if (GetClientRect(g_GameWindow, &gameRect)) {
            g_ScreenWidth = gameRect.right - gameRect.left;
            g_ScreenHeight = gameRect.bottom - gameRect.top;
        }

        if (g_DebugFile) {
            fprintf(g_DebugFile, "Found AssaultCube window! Size: %dx%d\n", g_ScreenWidth, g_ScreenHeight);
            fflush(g_DebugFile);
        }
    } else {
        if (g_DebugFile) {
            fprintf(g_DebugFile, "Warning: Could not find AssaultCube window!\n");
            fflush(g_DebugFile);
        }
    }

    // Set initial window state
    UpdateWindowTransparency();

    MSG msg;
    ZeroMemory(&msg, sizeof(msg));

    while (msg.message != WM_QUIT) {
        if (PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }

        // Handle menu toggle
        if (GetAsyncKeyState(VK_INSERT) & 1) {
            g_ShowMenu = !g_ShowMenu;
            UpdateWindowTransparency();
        }

        // Update overlay position to follow game window
        UpdateOverlayPosition();

        // Start ImGui frame
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // Read game data
        playerCount = mem.Read<int>(moduleBase + Offsets::PlayerCount);
        localPlayerAddr = mem.Read<uintptr_t>(moduleBase + Offsets::LocalPlayer);

        // Debug: Show what offsets we're actually using
        static int offsetDebugCounter = 0;
        if (offsetDebugCounter++ % 300 == 0 && g_DebugFile) {
            fprintf(g_DebugFile, "MAIN LOOP USING: PlayerCount offset=0x%X, LocalPlayer offset=0x%X\n",
                    Offsets::PlayerCount, Offsets::LocalPlayer);
            fprintf(g_DebugFile, "MAIN LOOP READING: PlayerCount=%d, LocalPlayer=0x%p\n",
                    playerCount, (void*)localPlayerAddr);
            fflush(g_DebugFile);
        }

        // Read view matrix
        for (int i = 0; i < 16; i++) {
            viewMatrix[i] = mem.Read<float>(moduleBase + Offsets::ViewMatrix + i * sizeof(float));
        }

        // Debug: Scan for ViewMatrix in memory
        static int vmScanCounter = 0;
        if (g_DebugFile && vmScanCounter++ % 120 == 0) {
            fprintf(g_DebugFile, "VIEWMATRIX SCAN: Searching for valid matrix...\n");

            // Test multiple potential ViewMatrix offsets
            uintptr_t testOffsets[] = {
                0x501AE8,   // Original
                0x10F580,   // Previous attempt
                0x17E0A8,   // Common AC offset
                0x17DFD8,   // Alternative
                0x101AE8,   // Modified original
                0x401AE8,   // Another variant
                0x50F580,   // Combination
                0x18AC04,   // Near other working offsets
                0x18AC08,   // Slight variation
                0x18AC0C    // Another variation
            };

            for (int i = 0; i < 10; i++) {
                float testMatrix[16];
                for (int j = 0; j < 16; j++) {
                    testMatrix[j] = mem.Read<float>(moduleBase + testOffsets[i] + j * sizeof(float));
                }

                // Check if this looks like a valid view matrix
                bool hasNonZero = false;
                bool hasReasonableValues = true;
                for (int j = 0; j < 16; j++) {
                    if (testMatrix[j] != 0.0f) hasNonZero = true;
                    if (abs(testMatrix[j]) > 1000.0f) hasReasonableValues = false;
                }

                if (hasNonZero && hasReasonableValues) {
                    fprintf(g_DebugFile, "POTENTIAL MATRIX at 0x%X: [%.3f %.3f %.3f %.3f]\n",
                            testOffsets[i], testMatrix[0], testMatrix[1], testMatrix[2], testMatrix[3]);
                }
            }
            fflush(g_DebugFile);
        }

        // Debug output (only show occasionally to avoid spam)
        static int debugCounter = 0;
        static int lastEntitiesProcessed = 0;
        if (debugCounter++ % 60 == 0 && g_DebugFile) { // Every ~1 second at 60fps
            fprintf(g_DebugFile, "Players: %d, LocalPlayer: 0x%p\n", playerCount, (void*)localPlayerAddr);
            fprintf(g_DebugFile, "Entities processed: %d\n", lastEntitiesProcessed);
            fprintf(g_DebugFile, "ModuleBase: 0x%p\n", (void*)moduleBase);

            // Test multiple known offsets
            fprintf(g_DebugFile, "=== OFFSET TESTING ===\n");

            // Test LocalPlayer offsets
            uintptr_t lp1 = mem.Read<uintptr_t>(moduleBase + 0x509B74); // Guided Hacking
            uintptr_t lp2 = mem.Read<uintptr_t>(moduleBase + 0x50F4F4); // Common AC 1.2
            uintptr_t lp3 = mem.Read<uintptr_t>(moduleBase + 0x10F4F4); // Alternative

            fprintf(g_DebugFile, "LocalPlayer 0x509B74: 0x%p\n", (void*)lp1);
            fprintf(g_DebugFile, "LocalPlayer 0x50F4F4: 0x%p\n", (void*)lp2);
            fprintf(g_DebugFile, "LocalPlayer 0x10F4F4: 0x%p\n", (void*)lp3);

            // Test PlayerCount offsets
            int pc1 = mem.Read<int>(moduleBase + 0x50F500);
            int pc2 = mem.Read<int>(moduleBase + 0x10F500);
            int pc3 = mem.Read<int>(moduleBase + 0x18AC0C);

            fprintf(g_DebugFile, "PlayerCount 0x50F500: %d (should be 8)\n", pc1);
            fprintf(g_DebugFile, "PlayerCount 0x10F500: %d (should be 8)\n", pc2);
            fprintf(g_DebugFile, "PlayerCount 0x18AC0C: %d (should be 8)\n", pc3);

            // Test EntityList offsets
            uintptr_t el1 = mem.Read<uintptr_t>(moduleBase + 0x50F4F8);
            uintptr_t el2 = mem.Read<uintptr_t>(moduleBase + 0x10F4F8);
            uintptr_t el3 = mem.Read<uintptr_t>(moduleBase + 0x18AC04);

            fprintf(g_DebugFile, "EntityList 0x50F4F8: 0x%p\n", (void*)el1);
            fprintf(g_DebugFile, "EntityList 0x10F4F8: 0x%p\n", (void*)el2);
            fprintf(g_DebugFile, "EntityList 0x18AC04: 0x%p\n", (void*)el3);

            // If we have a valid local player, test position reading
            if (lp1 > 0x400000) {
                float x = mem.Read<float>(lp1 + 0x4);
                float y = mem.Read<float>(lp1 + 0x8);
                float z = mem.Read<float>(lp1 + 0xC);
                fprintf(g_DebugFile, "LP1 Position: %.2f, %.2f, %.2f\n", x, y, z);
            }
            if (lp2 > 0x400000) {
                float x = mem.Read<float>(lp2 + 0x4);
                float y = mem.Read<float>(lp2 + 0x8);
                float z = mem.Read<float>(lp2 + 0xC);
                fprintf(g_DebugFile, "LP2 Position: %.2f, %.2f, %.2f\n", x, y, z);
            }

            if (localPlayerAddr) {
                Entity localPlayer(localPlayerAddr, mem);
                Vec3 pos = localPlayer.GetFootPosition();
                fprintf(g_DebugFile, "Local pos: %.2f, %.2f, %.2f\n", pos.x, pos.y, pos.z);
            }
            fflush(g_DebugFile);
        }

        // Draw menu if visible
        if (g_ShowMenu) {
            ImGui::SetNextWindowPos(ImVec2(50, 50), ImGuiCond_FirstUseEver);
            ImGui::SetNextWindowSize(ImVec2(350, 500), ImGuiCond_FirstUseEver);
            
            ImGui::Begin("Assault Cube ESP", &g_ShowMenu, ImGuiWindowFlags_NoCollapse);
            
            ImGui::Text("Press INSERT to toggle menu");
            ImGui::Separator();
            
            ImGui::Checkbox("Enable ESP", &config.enabled);
            
            if (config.enabled) {
                ImGui::Separator();
                ImGui::Text("Visual Options");
                ImGui::Checkbox("Boxes", &config.showBoxes);
                ImGui::Checkbox("Health Bar", &config.showHealth);
                ImGui::Checkbox("Names", &config.showName);
                ImGui::Checkbox("Distance", &config.showDistance);
                ImGui::Checkbox("Team", &config.showTeam);
                ImGui::Checkbox("Armor", &config.showArmor);
                ImGui::Checkbox("Snaplines", &config.showSnaplines);
                ImGui::Checkbox("Head Dot", &config.showHeadDot);

                ImGui::Separator();
                ImGui::Text("Cheat Features");
                ImGui::Checkbox("No Recoil", &config.noRecoil);
                ImGui::Checkbox("Aimbot", &config.aimbot);
                if (config.aimbot) {
                    ImGui::SliderFloat("Aimbot FOV", &config.aimbotFOV, 10.0f, 180.0f, "%.1f°");
                    ImGui::SliderFloat("Aimbot Smooth", &config.aimbotSmooth, 1.0f, 20.0f, "%.1f");
                    ImGui::Text("Aimbot Key: Right Mouse Button");
                }

                ImGui::Separator();
                ImGui::Text("Advanced Options");
                ImGui::Checkbox("Weapons", &config.showWeapons);
                ImGui::Checkbox("Ammo", &config.showAmmo);
                ImGui::Checkbox("State", &config.showState);
                ImGui::Checkbox("Show Status", &config.showStatus);
                
                ImGui::Separator();
                ImGui::Text("Visual Settings");
                ImGui::SliderFloat("Box Thickness", &config.boxThickness, 0.5f, 5.0f);
                ImGui::SliderFloat("Snapline Thickness", &config.snaplineThickness, 0.5f, 3.0f);
                
                const char* snaplinePositions[] = { "Top", "Center", "Bottom" };
                ImGui::Combo("Snapline Position", &config.snaplinePos, snaplinePositions, IM_ARRAYSIZE(snaplinePositions));
                
                ImGui::Separator();
                ImGui::Text("Colors");
                ImGui::ColorEdit4("Enemy Color", (float*)&config.enemyColor);
                ImGui::ColorEdit4("Team Color", (float*)&config.teamColor);
                ImGui::ColorEdit4("Text Color", (float*)&config.textColor);
                ImGui::ColorEdit4("Snapline Color", (float*)&config.snaplineColor);
            }
            
            ImGui::Separator();
            ImGui::Text("Players: %d", playerCount);
            
            ImGui::End();
        }

        // Debug: Check if ESP section is being entered
        if (g_DebugFile && debugCounter % 60 == 0) {
            fprintf(g_DebugFile, "ESP Check: enabled=%s, localPlayer=0x%p, playerCount=%d\n",
                    config.enabled ? "YES" : "NO", (void*)localPlayerAddr, playerCount);
            fflush(g_DebugFile);
        }

        // Draw ESP if enabled
        if (config.enabled && localPlayerAddr && playerCount > 0) {
            Entity localPlayer(localPlayerAddr, mem);
            Vec3 localPos = localPlayer.GetFootPosition();

            // Skip if local player position is invalid
            if (localPos.x == 0 && localPos.y == 0 && localPos.z == 0) {
                ImGui::Render();
                const float clear_color[4] = { 0.0f, 0.0f, 0.0f, 0.0f };
                g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, NULL);
                g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color);
                ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());
                g_pSwapChain->Present(1, 0);
                continue;
            }

            // Get draw list for ESP
            ImDrawList* drawList = ImGui::GetBackgroundDrawList();
            
            // Calculate snapline origin based on setting
            float snaplineY = 0;
            switch (config.snaplinePos) {
                case 0: snaplineY = 0; break; // Top
                case 1: snaplineY = g_ScreenHeight / 2.0f; break; // Center
                case 2: snaplineY = g_ScreenHeight - 1.0f; break; // Bottom
            }
            ImVec2 snaplineOrigin(g_ScreenWidth / 2.0f, snaplineY);
            
            // Loop through entities
            int entitiesProcessed = 0;
            uintptr_t entityListBase = mem.Read<uintptr_t>(moduleBase + Offsets::EntityList);

            // Always log entity list info for debugging
            if (g_DebugFile && debugCounter % 60 == 0) {
                fprintf(g_DebugFile, "EntityList offset: 0x%X, EntityList base: 0x%p\n",
                        Offsets::EntityList, (void*)entityListBase);

                // Test first few entity addresses
                for (int i = 0; i < 5 && i < playerCount; i++) {
                    uintptr_t entityAddr = mem.Read<uintptr_t>(entityListBase + i * 0x4);
                    fprintf(g_DebugFile, "Entity[%d]: 0x%p\n", i, (void*)entityAddr);
                }
                fflush(g_DebugFile);
            }

            for (int i = 0; i < playerCount && i < 32; i++) {
                uintptr_t entityAddr = mem.Read<uintptr_t>(entityListBase + i * 0x4);

                // Debug: Log entity details (simplified)
                static int entityDebugCounter = 0;
                if (g_DebugFile && entityDebugCounter++ % 300 == 0 && i < 5) {
                    fprintf(g_DebugFile, "ENTITY DEBUG [%d]: 0x%p", i, (void*)entityAddr);
                    if (!entityAddr) {
                        fprintf(g_DebugFile, " - NULL\n");
                    } else if (entityAddr == localPlayerAddr) {
                        fprintf(g_DebugFile, " - LOCAL PLAYER\n");
                    } else {
                        Entity testEntity(entityAddr, mem);
                        int health = testEntity.GetHealth();
                        int team = testEntity.GetTeam();
                        bool alive = testEntity.IsAlive();
                        fprintf(g_DebugFile, " - H:%d T:%d A:%s\n",
                                health, team, alive ? "Y" : "N");
                    }
                    fflush(g_DebugFile);
                }

                if (!entityAddr || entityAddr == localPlayerAddr) continue;

                Entity entity(entityAddr, mem);

                // Temporary: Process all entities with health > 0, bypass IsAlive() check
                int health = entity.GetHealth();
                int team = entity.GetTeam();
                if (health <= 0 || team == 0) continue;

                entitiesProcessed++;
                
                // Get positions
                Vec3 headPos = entity.GetHeadPosition();
                Vec3 footPos = entity.GetFootPosition();
                
                // World to screen for head and foot using proper ViewMatrix
                vec screenHead, screenFoot;
                bool headVisible = Math::WorldToScreen(headPos, screenHead, viewMatrix, g_ScreenWidth, g_ScreenHeight);
                bool footVisible = Math::WorldToScreen(footPos, screenFoot, viewMatrix, g_ScreenWidth, g_ScreenHeight);

                // Debug: Log WorldToScreen results
                static int w2sDebugCounter = 0;
                if (g_DebugFile && w2sDebugCounter++ % 60 == 0 && entitiesProcessed <= 2) {
                    fprintf(g_DebugFile, "W2S DEBUG: Head(%.1f,%.1f,%.1f)->Screen(%.1f,%.1f) Visible:%s\n",
                            headPos.x, headPos.y, headPos.z, screenHead.x, screenHead.y, headVisible ? "Y" : "N");
                    fprintf(g_DebugFile, "W2S DEBUG: Foot(%.1f,%.1f,%.1f)->Screen(%.1f,%.1f) Visible:%s\n",
                            footPos.x, footPos.y, footPos.z, screenFoot.x, screenFoot.y, footVisible ? "Y" : "N");
                    fflush(g_DebugFile);
                }

                if (!headVisible && !footVisible) continue;
                
                // Calculate distance
                float distance = Math::Distance(static_cast<vec>(localPos), static_cast<vec>(footPos));
                
                // Determine color based on team
                bool isEnemy = entity.GetTeam() != localPlayer.GetTeam();
                ImU32 boxColor = isEnemy ? 
                    ImGui::ColorConvertFloat4ToU32(config.enemyColor) : 
                    ImGui::ColorConvertFloat4ToU32(config.teamColor);
                ImU32 textColor = ImGui::ColorConvertFloat4ToU32(config.textColor);
                ImU32 snapColor = ImGui::ColorConvertFloat4ToU32(config.snaplineColor);
                
                // Dynamic box sizing based on distance (reuse existing distance calculation)
                float baseHeight = 100.0f;  // Base height for close players
                float scaleFactor = max(0.3f, min(2.0f, 50.0f / distance));  // Scale based on distance
                float height = baseHeight * scaleFactor;

                // Calculate dynamic width
                float width = height * 0.4f;

                // Use actual head/foot positions if they provide good height, otherwise use calculated height
                float actualHeight = screenFoot.y - screenHead.y;
                if (actualHeight > 20.0f && actualHeight < 200.0f) {
                    height = actualHeight;
                    width = height * 0.4f;
                } else {
                    // Adjust foot position to match calculated height
                    screenFoot.y = screenHead.y + height;
                }

                // Move boxes down slightly to better align with players
                float verticalOffset = height * 0.1f;  // Dynamic offset based on box size
                screenHead.y += verticalOffset;
                screenFoot.y += verticalOffset;

                // Ensure box is visible (move down if too close to top)
                if (screenHead.y < 10.0f) {
                    float offset = 10.0f - screenHead.y;
                    screenHead.y += offset;
                    screenFoot.y += offset;
                }

                ImVec2 topLeft(screenHead.x - width / 2, screenHead.y);
                ImVec2 bottomRight(screenHead.x + width / 2, screenFoot.y);
                
                // Draw ESP box
                if (config.showBoxes) {
                    drawList->AddRect(topLeft, bottomRight, boxColor, 0.0f, 0, config.boxThickness);
                }
                
                // Draw health bar
                if (config.showHealth) {
                    int health = entity.GetHealth();
                    float healthPercent = health / 100.0f;
                    
                    float barWidth = 4.0f;
                    float barHeight = height;
                    ImVec2 healthBarTop(topLeft.x - 8.0f, topLeft.y);
                    ImVec2 healthBarBottom(healthBarTop.x + barWidth, topLeft.y + barHeight);
                    
                    // Background
                    drawList->AddRectFilled(healthBarTop, healthBarBottom, IM_COL32(0, 0, 0, 180));
                    
                    // Health bar
                    ImVec2 healthFillBottom(healthBarBottom.x, healthBarTop.y + (barHeight * healthPercent));
                    ImU32 healthColor = IM_COL32(
                        (int)(255 * (1.0f - healthPercent)),
                        (int)(255 * healthPercent),
                        0, 255
                    );
                    drawList->AddRectFilled(healthBarTop, healthFillBottom, healthColor);
                }
                
                // Draw armor bar
                if (config.showArmor) {
                    int armor = entity.GetArmor();
                    if (armor > 0) {
                        float armorPercent = armor / 100.0f;
                        
                        float barWidth = 4.0f;
                        float barHeight = height;
                        ImVec2 armorBarTop(bottomRight.x + 4.0f, topLeft.y);
                        ImVec2 armorBarBottom(armorBarTop.x + barWidth, topLeft.y + barHeight);
                        
                        // Background
                        drawList->AddRectFilled(armorBarTop, armorBarBottom, IM_COL32(0, 0, 0, 180));
                        
                        // Armor bar
                        ImVec2 armorFillBottom(armorBarBottom.x, armorBarTop.y + (barHeight * armorPercent));
                        drawList->AddRectFilled(armorBarTop, armorFillBottom, IM_COL32(66, 135, 245, 255));
                    }
                }
                
                // Draw snapline
                if (config.showSnaplines) {
                    drawList->AddLine(snaplineOrigin, ImVec2(screenHead.x, screenHead.y), snapColor, config.snaplineThickness);
                }
                
                // Draw head dot
                if (config.showHeadDot) {
                    drawList->AddCircleFilled(ImVec2(screenHead.x, screenHead.y), 3.0f, boxColor);
                }
                
                // Draw text info
                float textY = bottomRight.y + 2;
                
                if (config.showName) {
                    std::string name = entity.GetName();
                    drawList->AddText(ImVec2(screenHead.x - (name.length() * 3), textY), textColor, name.c_str());
                    textY += 15;
                }
                
                if (config.showDistance) {
                    char distStr[32];
                    sprintf_s(distStr, "%.1fm", distance);
                    drawList->AddText(ImVec2(screenHead.x - 15, textY), textColor, distStr);
                    textY += 15;
                }
                
                if (config.showTeam) {
                    char teamStr[32];
                    sprintf_s(teamStr, "Team %d", entity.GetTeam());
                    drawList->AddText(ImVec2(screenHead.x - 20, textY), textColor, teamStr);
                    textY += 15;
                }
                
                if (config.showWeapons || config.showAmmo) {
                    // Show current weapon info based on what has ammo
                    char weaponStr[128] = "";
                    
                    if (entity.GetCarbineClip() > 0 || entity.GetCarbineAmmo() > 0) {
                        sprintf_s(weaponStr, "Carbine: %d/%d", entity.GetCarbineClip(), entity.GetCarbineAmmo());
                    }
                    else if (entity.GetShotgunClip() > 0 || entity.GetShotgunAmmo() > 0) {
                        sprintf_s(weaponStr, "Shotgun: %d/%d", entity.GetShotgunClip(), entity.GetShotgunAmmo());
                    }
                    else if (entity.GetMachineGunClip() > 0 || entity.GetMachineGunAmmo() > 0) {
                        sprintf_s(weaponStr, "SMG: %d/%d", entity.GetMachineGunClip(), entity.GetMachineGunAmmo());
                    }
                    else if (entity.GetSniperClip() > 0 || entity.GetSniperAmmo() > 0) {
                        sprintf_s(weaponStr, "Sniper: %d/%d", entity.GetSniperClip(), entity.GetSniperAmmo());
                    }
                    else if (entity.GetAssaultRifleClip() > 0 || entity.GetAssaultRifleAmmo() > 0) {
                        sprintf_s(weaponStr, "AR: %d/%d", entity.GetAssaultRifleClip(), entity.GetAssaultRifleAmmo());
                    }
                    else {
                        sprintf_s(weaponStr, "Pistol: %d/%d", entity.GetPistolClip(), entity.GetPistolAmmo());
                    }
                    
                    if (strlen(weaponStr) > 0) {
                        drawList->AddText(ImVec2(screenHead.x - 30, textY), textColor, weaponStr);
                        textY += 15;
                    }
                }
                
                if (config.showState) {
                    if (entity.GetMouseDown()) {
                        drawList->AddText(ImVec2(screenHead.x - 20, textY), IM_COL32(255, 0, 0, 255), "FIRING");
                    }
                }
            }

            // Cheat features (inside ESP block where localPlayer is available)
            if (config.noRecoil) {
                // No recoil - zero out recoil values
                mem.Write<float>(localPlayerAddr + Offsets::Entity::RecoilPitch, 0.0f);
                mem.Write<float>(localPlayerAddr + Offsets::Entity::RecoilYaw, 0.0f);
            }

            // Simple aimbot
            if (config.aimbot && (GetAsyncKeyState(config.aimbotKey) & 0x8000)) {
                Vec3 localHeadPos = localPlayer.GetHeadPosition();
                Vec2 localAngles = localPlayer.GetViewAngles();

                float closestDistance = FLT_MAX;
                Vec3 targetPos;
                bool hasTarget = false;

                // Find closest enemy in FOV
                for (int i = 0; i < playerCount; i++) {
                    uintptr_t entityAddr = mem.Read<uintptr_t>(entityListBase + i * sizeof(uintptr_t));
                    if (!entityAddr || entityAddr == localPlayerAddr) continue;

                    Entity entity(entityAddr, mem);
                    int health = entity.GetHealth();
                    int team = entity.GetTeam();
                    if (health <= 0 || team == 0) continue;

                    // Only target enemies
                    bool isEnemy = entity.GetTeam() != localPlayer.GetTeam();
                    if (!isEnemy) continue;

                    Vec3 enemyHead = entity.GetHeadPosition();
                    float distance = Math::Distance(vec(localHeadPos.x, localHeadPos.y, localHeadPos.z), vec(enemyHead.x, enemyHead.y, enemyHead.z));

                    // Calculate angle to target
                    Vec2 targetAngles = Math::CalcAngles(vec(localHeadPos.x, localHeadPos.y, localHeadPos.z), vec(enemyHead.x, enemyHead.y, enemyHead.z));

                    // Check if target is within FOV
                    float angleDiff = sqrtf(powf(Math::AngleDifference(targetAngles.x, localAngles.x), 2) +
                                         powf(Math::AngleDifference(targetAngles.y, localAngles.y), 2));

                    if (angleDiff <= config.aimbotFOV && distance < closestDistance) {
                        closestDistance = distance;
                        targetPos = enemyHead;
                        hasTarget = true;
                    }
                }

                // Aim at target with smoothing
                if (hasTarget) {
                    Vec2 targetAngles = Math::CalcAngles(vec(localHeadPos.x, localHeadPos.y, localHeadPos.z), vec(targetPos.x, targetPos.y, targetPos.z));

                    // Smooth aiming
                    float pitchDiff = Math::AngleDifference(targetAngles.x, localAngles.x);
                    float yawDiff = Math::AngleDifference(targetAngles.y, localAngles.y);

                    float newPitch = localAngles.x + (pitchDiff / config.aimbotSmooth);
                    float newYaw = localAngles.y + (yawDiff / config.aimbotSmooth);

                    // Clamp angles
                    newPitch = Math::ClampAngle(newPitch, -89.0f, 89.0f);
                    newYaw = Math::NormalizeAngle(newYaw);

                    // Write new angles
                    mem.Write<float>(localPlayerAddr + Offsets::Entity::Pitch, newPitch);
                    mem.Write<float>(localPlayerAddr + Offsets::Entity::Yaw, newYaw);
                }
            }

            // Update debug counter
            lastEntitiesProcessed = entitiesProcessed;
        }

        // Optional: Draw overlay status (can be removed for production)
        if (config.showStatus) {
            ImDrawList* statusDrawList = ImGui::GetBackgroundDrawList();
            statusDrawList->AddText(ImVec2(10, 10), IM_COL32(0, 255, 0, 255), "ESP Active");
        }



        // Rendering
        ImGui::Render();
        const float clear_color[4] = { 0.0f, 0.0f, 0.0f, 0.0f };
        g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, NULL);
        g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color);
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

        g_pSwapChain->Present(1, 0); // Present with vsync
    }

    // Cleanup
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    DestroyWindow(hwnd);
    UnregisterClass(wc.lpszClassName, wc.hInstance);

    // Close debug file
    if (g_DebugFile) {
        fclose(g_DebugFile);
    }

    return 0;
}

void UpdateOverlayPosition() {
    if (!g_GameWindow) return;

    RECT gameRect;
    if (GetWindowRect(g_GameWindow, &gameRect)) {
        // Position overlay exactly over the game window
        SetWindowPos(hwnd, HWND_TOPMOST,
                    gameRect.left, gameRect.top,
                    gameRect.right - gameRect.left,
                    gameRect.bottom - gameRect.top,
                    SWP_NOACTIVATE);
    }
}

void UpdateWindowTransparency() {
    if (g_ShowMenu) {
        // When menu is shown, window should be interactable
        SetWindowLong(hwnd, GWL_EXSTYLE, WS_EX_TOPMOST | WS_EX_LAYERED | WS_EX_TOOLWINDOW);
        SetLayeredWindowAttributes(hwnd, 0, 255, LWA_ALPHA);
    }
    else {
        // When menu is hidden, window should be click-through
        SetWindowLong(hwnd, GWL_EXSTYLE, WS_EX_TOPMOST | WS_EX_TRANSPARENT | WS_EX_LAYERED | WS_EX_TOOLWINDOW);
        SetLayeredWindowAttributes(hwnd, RGB(0, 0, 0), 255, LWA_COLORKEY | LWA_ALPHA);
    }
}

bool CreateDeviceD3D(HWND hWnd) {
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;

    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = {
        D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0,
    };

    if (D3D11CreateDeviceAndSwapChain(NULL, D3D_DRIVER_TYPE_HARDWARE, NULL,
        createDeviceFlags, featureLevelArray, 2,
        D3D11_SDK_VERSION, &sd, &g_pSwapChain,
        &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext) != S_OK)
        return false;

    CreateRenderTarget();
    return true;
}

void CreateRenderTarget() {
    ID3D11Texture2D* pBackBuffer;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, NULL, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void CleanupRenderTarget() {
    if (g_mainRenderTargetView) {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = NULL;
    }
}

void CleanupDeviceD3D() {
    CleanupRenderTarget();
    if (g_pSwapChain) { g_pSwapChain->Release(); g_pSwapChain = NULL; }
    if (g_pd3dDeviceContext) { g_pd3dDeviceContext->Release(); g_pd3dDeviceContext = NULL; }
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = NULL; }
}

LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg) {
    case WM_SIZE:
        if (g_pd3dDevice != NULL && wParam != SIZE_MINIMIZED) {
            CleanupRenderTarget();
            g_pSwapChain->ResizeBuffers(0, (UINT)LOWORD(lParam), (UINT)HIWORD(lParam), DXGI_FORMAT_UNKNOWN, 0);
            CreateRenderTarget();
        }
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
            return 0;
        break;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}
