<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="ImGui">
      <UniqueIdentifier>{A5E0B934-9AE9-4E6C-B9D4-238F6A9B4E11}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui.cpp">
      <Filter>ImGui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_demo.cpp">
      <Filter>ImGui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_draw.cpp">
      <Filter>ImGui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_tables.cpp">
      <Filter>ImGui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_widgets.cpp">
      <Filter>ImGui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_impl_dx11.cpp">
      <Filter>ImGui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_impl_win32.cpp">
      <Filter>ImGui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\freetype\imgui_freetype.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_stdlib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="memory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="entity.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="math.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imconfig.h">
      <Filter>ImGui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui.h">
      <Filter>ImGui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_internal.h">
      <Filter>ImGui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_impl_dx11.h">
      <Filter>ImGui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_impl_win32.h">
      <Filter>ImGui</Filter>
    </ClInclude>
    <ClInclude Include="minhook\include\MinHook.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\freetype\imgui_freetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_single_file.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_stdlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_rectpack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_textedit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_truetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Library Include="minhook\lib\libMinHook.x86.lib" />
  </ItemGroup>
</Project>