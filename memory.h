#pragma once
#include <Windows.h>
#include <TlHelp32.h>
#include <vector>
#include <iostream>

class Memory {
private:
    HANDLE processHandle;
    DWORD processId;
    uintptr_t moduleBase;

public:
    Memory() : processHandle(NULL), processId(0), moduleBase(0) {}
    
    ~Memory() {
        if (processHandle && processHandle != INVALID_HANDLE_VALUE) {
            CloseHandle(processHandle);
        }
    }

    bool Attach(const wchar_t* processName, const wchar_t* moduleName) {
        // Clean up any existing handle
        if (processHandle && processHandle != INVALID_HANDLE_VALUE) {
            CloseHandle(processHandle);
            processHandle = NULL;
        }
        
        processId = GetProcessIdByName(processName);
        if (!processId) {
            std::cerr << "Failed to find process: " << processName << std::endl;
            return false;
        }

        processHandle = OpenProcess(PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION, FALSE, processId);
        if (!processHandle) {
            std::cerr << "Failed to open process. Error: " << GetLastError() << std::endl;
            return false;
        }

        moduleBase = GetModuleBaseAddress(moduleName);
        if (!moduleBase) {
            std::cerr << "Failed to find module: " << moduleName << std::endl;
            CloseHandle(processHandle);
            processHandle = NULL;
            return false;
        }
        
        return true;
    }

    template<typename T>
    T Read(uintptr_t address) {
        T value = {};
        if (!processHandle || processHandle == INVALID_HANDLE_VALUE) {
            return value;
        }

        SIZE_T bytesRead;
        if (!ReadProcessMemory(processHandle, (LPCVOID)address, &value, sizeof(T), &bytesRead) || bytesRead != sizeof(T)) {
            // Read failed, return default value
            return T{};
        }
        return value;
    }

    template<typename T>
    bool Write(uintptr_t address, T value) {
        if (!processHandle || processHandle == INVALID_HANDLE_VALUE) {
            return false;
        }

        SIZE_T bytesWritten;
        return WriteProcessMemory(processHandle, (LPVOID)address, &value, sizeof(T), &bytesWritten) && bytesWritten == sizeof(T);
    }



    uintptr_t GetModuleBase() const { return moduleBase; }
    bool IsAttached() const { return processHandle != NULL && processHandle != INVALID_HANDLE_VALUE && moduleBase != 0; }

private:
    DWORD GetProcessIdByName(const wchar_t* processName) {
        DWORD processId = 0;
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot != INVALID_HANDLE_VALUE) {
            PROCESSENTRY32W processEntry;
            processEntry.dwSize = sizeof(processEntry);

            if (Process32FirstW(snapshot, &processEntry)) {
                do {
                    if (_wcsicmp(processEntry.szExeFile, processName) == 0) {
                        processId = processEntry.th32ProcessID;
                        break;
                    }
                } while (Process32NextW(snapshot, &processEntry));
            }
            CloseHandle(snapshot);
        }
        return processId;
    }

    uintptr_t GetModuleBaseAddress(const wchar_t* moduleName) {
        uintptr_t moduleBase = 0;
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, processId);
        if (snapshot != INVALID_HANDLE_VALUE) {
            MODULEENTRY32W moduleEntry;
            moduleEntry.dwSize = sizeof(moduleEntry);

            if (Module32FirstW(snapshot, &moduleEntry)) {
                do {
                    if (_wcsicmp(moduleEntry.szModule, moduleName) == 0) {
                        moduleBase = (uintptr_t)moduleEntry.modBaseAddr;
                        break;
                    }
                } while (Module32NextW(snapshot, &moduleEntry));
            }
            CloseHandle(snapshot);
        }
        return moduleBase;
    }
};
