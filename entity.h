#pragma once
#include <Windows.h>
#include <string>

// Game offsets for AssaultCube 1.2.0.2 (WORKING OFFSETS - verified by testing)
namespace Offsets {
    constexpr auto LocalPlayer = 0x10F4F4;  // Player Base (VERIFIED WORKING)
    constexpr auto EntityList = 0x10F4F8;   // Entity Base (updated to match)
    constexpr auto ViewMatrix = 0x501AE8;    // View matrix offset
    constexpr auto PlayerCount = 0x10F500;  // Player count (VERIFIED WORKING - shows 12 players)

    namespace Entity {
        // Position offsets (from Guided Hacking Academy tutorial)
        // Structure: char unknown1[4]; float x; float y; float z; char unknown2[0x30]; float yaw; float pitch;
        constexpr auto HeadPos = 0x04;       // Head position (X at +0x4, Y at +0x8, Z at +0xC)
        constexpr auto FootPos = 0x04;       // Position X, Y, Z (same as head in tutorial)

        // View angles (from tutorial structure)
        constexpr auto Yaw = 0x40;           // Yaw angle (base + 4 + 4 + 4 + 4 + 0x30)
        constexpr auto Pitch = 0x44;         // Pitch angle (yaw + 4)
        constexpr auto Roll = 0x48;          // Roll angle

        // Player info (from tutorial - need to find exact offsets)
        constexpr auto Health = 0xF8;        // Health value
        constexpr auto Armor = 0xFC;         // Armor value
        constexpr auto IsNotInGame = 0x70;   // Is not in game flag
        constexpr auto Name = 0x225;         // Player name (char[16])
        constexpr auto Team = 0x32C;         // Team value
        constexpr auto IsDead = 0x334;       // Dead flag (from tutorial: yaw + pitch + 0x2f0)

        // Weapons (updated for AC 1.2)
        constexpr auto Pistol = 0x12C;       // Pistol ammo
        constexpr auto Carbine = 0x150;      // Carbine ammo
        constexpr auto Shotgun = 0x134;      // Shotgun ammo
        constexpr auto MachineGun = 0x138;   // SMG ammo
        constexpr auto Sniper = 0x13C;       // Sniper ammo
        constexpr auto AssaultRifle = 0x140; // Assault rifle ammo
        constexpr auto Akimbo = 0x148;       // Akimbo ammo

        // Weapon clips (updated for AC 1.2)
        constexpr auto PistolClip = 0x16C;       // Pistol clip
        constexpr auto CarbineClip = 0x190;      // Carbine clip
        constexpr auto ShotgunClip = 0x174;      // Shotgun clip
        constexpr auto MachineGunClip = 0x178;   // SMG clip
        constexpr auto SniperClip = 0x17C;       // Sniper clip
        constexpr auto AssaultRifleClip = 0x180; // Assault rifle clip
        constexpr auto GrenadeAmmo = 0x144;      // Grenade ammo
        constexpr auto AkimboClip = 0x188;       // Akimbo clip

        // Weapon timers (updated for AC 1.2)
        constexpr auto KnifeTimer = 0x1B0;       // Knife timer
        constexpr auto PistolTimer = 0x1B4;      // Pistol timer
        constexpr auto CarbineTimer = 0x1D8;     // Carbine timer
        constexpr auto ShotgunTimer = 0x1BC;     // Shotgun timer
        constexpr auto MachineGunTimer = 0x1C0;  // SMG timer
        constexpr auto SniperTimer = 0x1C4;      // Sniper timer
        constexpr auto AssaultRifleTimer = 0x1C8; // Assault rifle timer
        constexpr auto GrenadeTimer = 0x1CC;     // Grenade timer

        // Additional info (updated for AC 1.2)
        constexpr auto MouseDown = 0x224;        // Mouse down state
    }
}

// Forward declare the game's vec struct
struct vec;

// Our wrapper structs that convert to game's vec
struct Vec3 {
    float x, y, z;
    Vec3() : x(0.f), y(0.f), z(0.f) {}
    Vec3(float x, float y, float z) : x(x), y(y), z(z) {}
    operator vec() const; // Conversion operator defined in math.h
};

struct Vec2 {
    float x, y;
    Vec2() : x(0.f), y(0.f) {}
    Vec2(float x, float y) : x(x), y(y) {}
};

class Entity {
private:
    uintptr_t address;
    class Memory& mem;

public:
    Entity(uintptr_t addr, Memory& memory) : address(addr), mem(memory) {}

    Vec3 GetHeadPosition() {
        return Vec3(
            mem.Read<float>(address + Offsets::Entity::HeadPos),
            mem.Read<float>(address + Offsets::Entity::HeadPos + 4),
            mem.Read<float>(address + Offsets::Entity::HeadPos + 8)
        );
    }

    Vec3 GetFootPosition() {
        return Vec3(
            mem.Read<float>(address + Offsets::Entity::FootPos),      // X at +0x4
            mem.Read<float>(address + Offsets::Entity::FootPos + 4),  // Y at +0x8
            mem.Read<float>(address + Offsets::Entity::FootPos + 8)   // Z at +0xC
        );
    }

    std::string GetName() {
        char name[260] = {0};
        for (int i = 0; i < 260; i++) {
            name[i] = mem.Read<char>(address + Offsets::Entity::Name + i);
            if (name[i] == '\0') break;
        }
        return std::string(name);
    }

    Vec2 GetViewAngles() {
        return Vec2(
            mem.Read<float>(address + Offsets::Entity::Yaw),
            mem.Read<float>(address + Offsets::Entity::Pitch)
        );
    }

    float GetRoll() { return mem.Read<float>(address + Offsets::Entity::Roll); }
    int GetHealth() { return mem.Read<int>(address + Offsets::Entity::Health); }
    int GetArmor() { return mem.Read<int>(address + Offsets::Entity::Armor); }
    int GetTeam() { return mem.Read<int>(address + Offsets::Entity::Team); }
    bool GetMouseDown() { return mem.Read<bool>(address + Offsets::Entity::MouseDown); }

    // Weapon ammo
    int GetPistolAmmo() { return mem.Read<int>(address + Offsets::Entity::Pistol); }
    int GetCarbineAmmo() { return mem.Read<int>(address + Offsets::Entity::Carbine); }
    int GetShotgunAmmo() { return mem.Read<int>(address + Offsets::Entity::Shotgun); }
    int GetMachineGunAmmo() { return mem.Read<int>(address + Offsets::Entity::MachineGun); }
    int GetSniperAmmo() { return mem.Read<int>(address + Offsets::Entity::Sniper); }
    int GetAssaultRifleAmmo() { return mem.Read<int>(address + Offsets::Entity::AssaultRifle); }
    int GetAkimboAmmo() { return mem.Read<int>(address + Offsets::Entity::Akimbo); }

    // Weapon clips
    int GetPistolClip() { return mem.Read<int>(address + Offsets::Entity::PistolClip); }
    int GetCarbineClip() { return mem.Read<int>(address + Offsets::Entity::CarbineClip); }
    int GetShotgunClip() { return mem.Read<int>(address + Offsets::Entity::ShotgunClip); }
    int GetMachineGunClip() { return mem.Read<int>(address + Offsets::Entity::MachineGunClip); }
    int GetSniperClip() { return mem.Read<int>(address + Offsets::Entity::SniperClip); }
    int GetAssaultRifleClip() { return mem.Read<int>(address + Offsets::Entity::AssaultRifleClip); }
    int GetGrenadeAmmo() { return mem.Read<int>(address + Offsets::Entity::GrenadeAmmo); }
    int GetAkimboClip() { return mem.Read<int>(address + Offsets::Entity::AkimboClip); }

    // Weapon timers
    float GetKnifeTimer() { return mem.Read<float>(address + Offsets::Entity::KnifeTimer); }
    float GetPistolTimer() { return mem.Read<float>(address + Offsets::Entity::PistolTimer); }
    float GetCarbineTimer() { return mem.Read<float>(address + Offsets::Entity::CarbineTimer); }
    float GetShotgunTimer() { return mem.Read<float>(address + Offsets::Entity::ShotgunTimer); }
    float GetMachineGunTimer() { return mem.Read<float>(address + Offsets::Entity::MachineGunTimer); }
    float GetSniperTimer() { return mem.Read<float>(address + Offsets::Entity::SniperTimer); }
    float GetAssaultRifleTimer() { return mem.Read<float>(address + Offsets::Entity::AssaultRifleTimer); }
    float GetGrenadeTimer() { return mem.Read<float>(address + Offsets::Entity::GrenadeTimer); }

    bool IsAlive() {
        // Simplified check - if entity has health and is on a team, consider it alive
        return GetHealth() > 0 && GetTeam() != 0;
    }
    uintptr_t GetAddress() const { return address; }
};
