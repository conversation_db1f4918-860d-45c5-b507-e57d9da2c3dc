#pragma once
#include <Windows.h>
#include <string>

// Game offsets
namespace Offsets {
    constexpr auto LocalPlayer = 0x10F4F4;  // Player Base
    constexpr auto EntityList = 0x10F4F8;   // Entity Base
    constexpr auto ViewMatrix = 0x501AE8;  // View matrix offset for AC 1.2
    constexpr auto PlayerCount = 0x10F500;

    namespace Entity {
        // Position offsets
        constexpr auto HeadPos = 0x04;
        constexpr auto FootPos = 0x34;
        
        // View angles
        constexpr auto Yaw = 0x40;
        constexpr auto Pitch = 0x44;
        constexpr auto Roll = 0x48;
        
        // Player info
        constexpr auto Health = 0x0EC;
        constexpr auto Armor = 0x0F0;
        constexpr auto IsNotInGame = 0x70;
        constexpr auto Name = 0x205;
        constexpr auto Team = 0x338;
        
        // Weapons
        constexpr auto Pistol = 0x374;
        constexpr auto Carbine = 0x378;
        constexpr auto Shotgun = 0x37C;
        constexpr auto MachineGun = 0x380;
        constexpr auto Sniper = 0x384;
        constexpr auto AssaultRifle = 0x388;
        constexpr auto Akimbo = 0x38C;
        
        // Weapon clips
        constexpr auto PistolClip = 0x390;
        constexpr auto CarbineClip = 0x394;
        constexpr auto ShotgunClip = 0x398;
        constexpr auto MachineGunClip = 0x39C;
        constexpr auto SniperClip = 0x3A0;
        constexpr auto AssaultRifleClip = 0x3A4;
        constexpr auto GrenadeAmmo = 0x3A8;
        constexpr auto AkimboClip = 0x3AC;
        
        // Weapon timers
        constexpr auto KnifeTimer = 0x3B0;
        constexpr auto PistolTimer = 0x3B4;
        constexpr auto CarbineTimer = 0x3B8;
        constexpr auto ShotgunTimer = 0x3BC;
        constexpr auto MachineGunTimer = 0x3C0;
        constexpr auto SniperTimer = 0x3C4;
        constexpr auto AssaultRifleTimer = 0x3C8;
        constexpr auto GrenadeTimer = 0x3CC;
        
        // Team info
        constexpr auto Team1 = 0x32C;
        constexpr auto MouseDown = 0x224;
        constexpr auto Team2 = 0x338;
    }
}

// Forward declare the game's vec struct
struct vec;

// Our wrapper structs that convert to game's vec
struct Vec3 {
    float x, y, z;
    Vec3() : x(0.f), y(0.f), z(0.f) {}
    Vec3(float x, float y, float z) : x(x), y(y), z(z) {}
    operator vec() const; // Conversion operator defined in math.h
};

struct Vec2 {
    float x, y;
    Vec2() : x(0.f), y(0.f) {}
    Vec2(float x, float y) : x(x), y(y) {}
};

class Entity {
private:
    uintptr_t address;
    class Memory& mem;

public:
    Entity(uintptr_t addr, Memory& memory) : address(addr), mem(memory) {}

    Vec3 GetHeadPosition() {
        return Vec3(
            mem.Read<float>(address + Offsets::Entity::HeadPos),
            mem.Read<float>(address + Offsets::Entity::HeadPos + 4),
            mem.Read<float>(address + Offsets::Entity::HeadPos + 8)
        );
    }

    Vec3 GetFootPosition() {
        return Vec3(
            mem.Read<float>(address + Offsets::Entity::FootPos),
            mem.Read<float>(address + Offsets::Entity::FootPos + 4),
            mem.Read<float>(address + Offsets::Entity::FootPos + 8)
        );
    }

    std::string GetName() {
        char name[260] = {0};
        for (int i = 0; i < 260; i++) {
            name[i] = mem.Read<char>(address + Offsets::Entity::Name + i);
            if (name[i] == '\0') break;
        }
        return std::string(name);
    }

    Vec2 GetViewAngles() {
        return Vec2(
            mem.Read<float>(address + Offsets::Entity::Yaw),
            mem.Read<float>(address + Offsets::Entity::Pitch)
        );
    }

    float GetRoll() { return mem.Read<float>(address + Offsets::Entity::Roll); }
    int GetHealth() { return mem.Read<int>(address + Offsets::Entity::Health); }
    int GetArmor() { return mem.Read<int>(address + Offsets::Entity::Armor); }
    int GetTeam() { return mem.Read<int>(address + Offsets::Entity::Team1); }
    bool GetMouseDown() { return mem.Read<bool>(address + Offsets::Entity::MouseDown); }

    // Weapon ammo
    int GetPistolAmmo() { return mem.Read<int>(address + Offsets::Entity::Pistol); }
    int GetCarbineAmmo() { return mem.Read<int>(address + Offsets::Entity::Carbine); }
    int GetShotgunAmmo() { return mem.Read<int>(address + Offsets::Entity::Shotgun); }
    int GetMachineGunAmmo() { return mem.Read<int>(address + Offsets::Entity::MachineGun); }
    int GetSniperAmmo() { return mem.Read<int>(address + Offsets::Entity::Sniper); }
    int GetAssaultRifleAmmo() { return mem.Read<int>(address + Offsets::Entity::AssaultRifle); }
    int GetAkimboAmmo() { return mem.Read<int>(address + Offsets::Entity::Akimbo); }

    // Weapon clips
    int GetPistolClip() { return mem.Read<int>(address + Offsets::Entity::PistolClip); }
    int GetCarbineClip() { return mem.Read<int>(address + Offsets::Entity::CarbineClip); }
    int GetShotgunClip() { return mem.Read<int>(address + Offsets::Entity::ShotgunClip); }
    int GetMachineGunClip() { return mem.Read<int>(address + Offsets::Entity::MachineGunClip); }
    int GetSniperClip() { return mem.Read<int>(address + Offsets::Entity::SniperClip); }
    int GetAssaultRifleClip() { return mem.Read<int>(address + Offsets::Entity::AssaultRifleClip); }
    int GetGrenadeAmmo() { return mem.Read<int>(address + Offsets::Entity::GrenadeAmmo); }
    int GetAkimboClip() { return mem.Read<int>(address + Offsets::Entity::AkimboClip); }

    // Weapon timers
    float GetKnifeTimer() { return mem.Read<float>(address + Offsets::Entity::KnifeTimer); }
    float GetPistolTimer() { return mem.Read<float>(address + Offsets::Entity::PistolTimer); }
    float GetCarbineTimer() { return mem.Read<float>(address + Offsets::Entity::CarbineTimer); }
    float GetShotgunTimer() { return mem.Read<float>(address + Offsets::Entity::ShotgunTimer); }
    float GetMachineGunTimer() { return mem.Read<float>(address + Offsets::Entity::MachineGunTimer); }
    float GetSniperTimer() { return mem.Read<float>(address + Offsets::Entity::SniperTimer); }
    float GetAssaultRifleTimer() { return mem.Read<float>(address + Offsets::Entity::AssaultRifleTimer); }
    float GetGrenadeTimer() { return mem.Read<float>(address + Offsets::Entity::GrenadeTimer); }

    bool IsAlive() { return GetHealth() > 0 && GetTeam() != 0; }
    uintptr_t GetAddress() const { return address; }
};
